# Multi-stage build for Python agent service
FROM python:3.11-slim AS base
WORKDIR /app

# Install system dependencies in base
RUN apt-get update && apt-get install -y --no-install-recommends \
    build-essential \
    && rm -rf /var/lib/apt/lists/*

# Install poetry
RUN pip install --no-cache-dir poetry==1.7.1

# Configure poetry to not create virtual environment
RUN poetry config virtualenvs.create false \
    && poetry config installer.max-workers 10

# Dependency stage
FROM base AS deps
COPY pyproject.toml poetry.lock ./
RUN poetry install --no-interaction --no-ansi --no-root --only=main

# Production stage
FROM python:3.11-slim AS production
WORKDIR /app

# Install runtime dependencies only
RUN apt-get update && apt-get install -y --no-install-recommends \
    && rm -rf /var/lib/apt/lists/*

# Copy installed packages from deps stage
COPY --from=deps /usr/local/lib/python3.11/site-packages/ /usr/local/lib/python3.11/site-packages/
COPY --from=deps /usr/local/bin/ /usr/local/bin/

# Copy only necessary source files
COPY src/ ./src/
COPY config/ ./config/
COPY .secrets/ ./.secrets/
COPY preview.html ./
COPY api.py ./

# Create non-root user for security
RUN groupadd -r appuser && useradd -r -g appuser appuser
RUN chown -R appuser:appuser /app
USER appuser

EXPOSE 8000
CMD ["uvicorn", "api:app", "--host", "0.0.0.0", "--port", "8000", "--proxy-headers", "--log-level", "info"]