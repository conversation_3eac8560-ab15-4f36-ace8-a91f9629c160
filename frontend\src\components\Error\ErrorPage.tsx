import { Box } from "@mui/material";
import ErrorMessage from '../Common/ErrorMessage';

const ErrorPage = ({ errorMessage, isMobile, updateErrorMessageState }) => {
    return (
        <>
            {errorMessage && (
                <Box
                    className="error-area-container"
                    sx={{
                        position: 'fixed',
                        top: '64px',
                        left: { xs: '24px', md: '20%' },
                        right: { xs: '24px', md: '48px' },
                        width: {
                            xs: 'calc(100% - 48px)',
                            sm: 'auto',
                            md: 'auto',
                        },
                        zIndex: 99,
                        padding: '16px',
                        boxSizing: 'border-box',
                        marginLeft: 'auto',
                        marginRight: 'auto',
                        [theme => theme.breakpoints.up('sm')]: {
                            left: 'auto',
                            right: 'auto',
                            width: '50%',
                        },
                    }}
                >
                    <ErrorMessage
                        error={errorMessage}
                        onClose={() => updateErrorMessageState('')}
                    />
                </Box>
            )}

            <Box
                component="img"
                src={'/Error.png'}
                alt="Error"
                sx={{
                    display: 'block',
                    maxWidth: '100%',
                    maxHeight: '100vh',
                    margin: 'auto',
                    justifyContent: 'center',
                    alignItems: 'center',
                }}
            />
        </>
    );
};

export default ErrorPage;