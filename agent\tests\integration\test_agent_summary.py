import asyncio
import sys
from pathlib import Path
import os
from dotenv import load_dotenv
import logging
from src.agent.main import Agent, agent_cache

# Configure logging
logging.basicConfig(level=logging.INFO)

# Add the project root to the Python path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))


async def test_agent_summary():
    # Load environment variables
    load_dotenv()

    # Set up config
    config = {
        "model_name": "gemini-2.0-flash-001",
        "temperature": 0.1,
        "max_tokens": 8192,
        "max_iterations": 10,
        "verbose": True,
        "bucket_name": "scihub-papers-processed",
        "use_gcp": True,
        "google_api_key": os.environ.get("GOOGLE_API_KEY"),
    }

    # Initialize agent with config and conversation_id
    conversation_id = "test-summary"
    agent = Agent(config=config, conversation_id=conversation_id)

    # Test query that should trigger structured data analysis
    query = "Show me the trends in stunting rates across different countries"

    print(f"\n{'=' * 50}")
    print(f"Testing query: {query}")
    print(f"{'=' * 50}")

    try:
        # Execute the query
        answer = await agent.execute(query)
        print(f"\nFinal Answer: {answer}")

        # Check tool cache
        tool_cache = agent.tool_manager.cache
        print("\nTool Cache Status:")
        print(f"RAG Results: {'Yes' if tool_cache.rag_results else 'No'}")
        print(f"Structured Data: {'Yes' if tool_cache.structured_data else 'No'}")
        print(f"Final Answer: {'Yes' if tool_cache.final_answer else 'No'}")

        # Show structured data output
        if tool_cache.structured_data:
            print("\nStructured Data Output:")
            if hasattr(tool_cache.structured_data, "prompt"):
                print("\nData Analysis Prompt:")
                print(tool_cache.structured_data.prompt)

        # Show summary results
        if tool_cache.final_answer:
            print("\nGenerated Final Answer:")
            print(tool_cache.final_answer.text)

        # check tool_cache.final_answer == answer
        if tool_cache.final_answer.text == answer:
            print(
                "\n**Agent's final answer generated using final answer generator tool!**"
            )
        else:
            print(
                "\n** Agent's final answer different than final answer generator tool**"
            )

        # Show reasoning history to verify tool sequence
        print("\nReasoning History:")
        history = agent_cache.get_or_init_history(conversation_id)
        for entry in history:
            print(f"\nIteration {entry.iteration}:")
            print(f"Thought: {entry.thought}")
            if entry.action:
                print(f"Action: {entry.action['name']}")
                print(f"Action Input: {entry.action.get('input', {})}")
                # Check if this is a final answer action
                if entry.action["name"] == "final_answer_generator":
                    print("Final Answer Generator Input:")
                    print(
                        f"Query: {entry.action['input'].get('user_query', 'No query')}"
                    )
                    print(
                        f"Reference Analysis: {entry.action['input'].get('structured_data', 'no structured_data')}"
                    )
            if entry.observation:
                print(f"Observation: {entry.observation[:200]}...")
            if entry.answer:
                print(f"Answer: {entry.answer}")

    except Exception as e:
        print(f"Error processing query: {e}")
    finally:
        await agent.cleanup()


if __name__ == "__main__":
    asyncio.run(test_agent_summary())
