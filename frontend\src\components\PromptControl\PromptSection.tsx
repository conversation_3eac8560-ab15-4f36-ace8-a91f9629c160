import { useMemo, useEffect, useState } from "react";
import { Grid, Box, Typography } from "@mui/material";
import PromptControl from "./PromptControl";
import { useIsMobile, useIsTablet } from "../../components/Layout/MobileUtils";

const PromptSection = ({
    centeredContent,
    handleChange,
    queryLabelText,
    isLoading,
    dataTags,
    selectedTag,
    onCloseDropdown,
    chipClickCount,
}) => {
    const [containerWidth, setContainerWidth] = useState<number | null>(null);
    const isMobile = useIsMobile();
    const isTablet = useIsTablet();
    const isMobileOrTablet = isMobile || isTablet;

    useEffect(() => {
        if (!centeredContent) {
            const chatContainer = document.querySelector(".messages-wrapper");
            if (chatContainer) {
                const updateWidth = () => {
                    setContainerWidth(chatContainer.clientWidth);
                };
                updateWidth();

                const resizeObserver = new ResizeObserver(entries => {
                    for (const entry of entries) {
                        updateWidth();
                    }
                });
                resizeObserver.observe(chatContainer);

                return () => resizeObserver.disconnect();
            } else {
                console.log(".messages-wrapper not found.");
            }
        } else {
            setContainerWidth(null);
        }
    }, [centeredContent]);

    const promptSectionStyle = useMemo(() => ({
        boxSizing: "border-box",
        width: containerWidth ? `${containerWidth}px` : (centeredContent && !isMobileOrTablet ? "718px" : "100%"),
        maxWidth: containerWidth ? `${containerWidth}px` : "100%",
        mx: centeredContent ? "auto" : "0",
        position: centeredContent ? "static" : "fixed",
        bottom: centeredContent ? "auto" : 0,
        zIndex: centeredContent ? "auto" : 1,
        pt: centeredContent ? 0 : "0px",
        pb: centeredContent ? 3 : "0px",
        px: centeredContent ? 2 : 0,
        mt: centeredContent ? 3 : 0,
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'space-between',
        height: 'auto',
    }), [centeredContent, containerWidth]);

    const disclaimerContainerStyle = {
        width: '100%',
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        paddingTop: '10px',
        paddingBottom: '10px',
    };

    return (
        <Box
            className="prompt-area-container"
            bgcolor="white"
            sx={promptSectionStyle}
        >
            <Grid container spacing={centeredContent ? 2 : 0} mb={0} sx={{ height: 'auto', display: 'flex', flexDirection: 'column', justifyContent: 'space-between' }}>
                <Grid item xs={12}>
                    <PromptControl
                        handleChange={handleChange}
                        labelText={queryLabelText}
                        loadingState={isLoading}
                        dataTags={dataTags}
                        centeredContent={centeredContent}
                        selectedTag={selectedTag}
                        onCloseDropdown={onCloseDropdown}
                        chipClickCount={chipClickCount}
                    />
                </Grid>
                {!centeredContent && (
                    <Grid item xs={12} sx={disclaimerContainerStyle}>
                        <Typography variant="body2" component="span" sx={{ fontSize: '12px', color: 'text.secondary', height: '20px' }}>
                            ImpactAI can make mistakes, so check outputs carefully
                        </Typography>
                    </Grid>
                )}
            </Grid>
        </Box>
    );
};

export default PromptSection;