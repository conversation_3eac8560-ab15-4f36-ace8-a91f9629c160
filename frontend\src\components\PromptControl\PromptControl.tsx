import React, { useState, useRef, useEffect } from "react";
import {
  FormControl,
  TextField,
  IconButton,
  Box,
  Autocomplete,
  Paper
} from "@mui/material";
import { useTheme } from "@mui/material/styles";
import ThickArrowRightIcon from "./ThickArrowRightIcon";
import { TagEntry } from "../../types/ConversationTypes";
import { useIsMobile, useIsTablet } from "../Layout/MobileUtils";

function fetchRandomXNumbers(list: any[], n: number): any[] {
  if (list.length <= n) {
    return list;
  }

  const shuffled = [...list].sort(() => 0.5 - Math.random());
  return shuffled.slice(0, n);
}

interface PromptControlProps {
  handleChange: (message: string, reason: string) => void;
  labelText: string;
  loadingState: boolean;
  dataTags: { [key: string]: TagEntry[] };
  centeredContent?: boolean;
  selectedTag: string | null;
  onCloseDropdown: () => void;
  chipClickCount: number;
}

const PromptControl: React.FC<PromptControlProps> = ({
  handleChange,
  labelText,
  loadingState,
  dataTags,
  centeredContent,
  selectedTag,
  onCloseDropdown,
  chipClickCount
}) => {
  const isMobile = useIsMobile();
  const isTablet = useIsTablet();
  const isMobileOrTablet = isMobile || isTablet;
  const [inputText, setInputText] = useState("");
  const [inputValue, setInputValue] = useState("");
  const isMultiline = inputText.includes('\n');
  const inputRef = useRef(null);
  const theme = useTheme();
  const [submitDisabled, setSubmitDisabled] = useState(false);
  const [openDropdown, setOpenDropdown] = useState(false);
  const [tagsData, setTagsData] = useState<TagEntry[]>([]);
  const [isTypingManually, setIsTypingManually] = useState(false);

  const [hoveredOptionIndex, setHoveredOptionIndex] = useState<number | null>(null);

  const isDisableTextQueryInProd = window.location.href.includes('impact-ai-prod.app');

  useEffect(() => {
    if (inputRef.current) {
      inputRef.current.focus();
    }
  }, []);

  useEffect(() => {
    if (selectedTag) {
      const relevantTags = dataTags[selectedTag] || [];
      const updatedTagsData = fetchRandomXNumbers(relevantTags, 4);
      setTagsData(updatedTagsData);
      setOpenDropdown(true);
      setIsTypingManually(false);
      setTimeout(() => {
        if (inputRef.current) {
          inputRef.current.focus();
          const enterEvent = new KeyboardEvent('keydown', { key: 'Enter', code: 'Enter', which: 13 });
          inputRef.current.dispatchEvent(enterEvent);
        }
      }, 0);
    }
  }, [selectedTag, dataTags, chipClickCount]);

  const handleInputChange = (event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>, newInputValue: string) => {
    if (isDisableTextQueryInProd && event.type === 'change' && event.target === inputRef.current) {
      return;
    }

    if (isTypingManually && newInputValue !== inputValue) {
      onCloseDropdown();
      setTagsData([]);
    }

    const newValue = event.target.value ?? newInputValue;
    setInputValue(newValue);
    setInputText(newValue);
    setOpenDropdown(newValue.length > 0);
  };

  const handleChangeDropdown = (event: React.ChangeEvent<{}>, newValue: TagEntry | null) => {
    if (typeof newValue === 'object' && newValue !== null && newValue.value) {
      setInputValue(newValue.value);
      setInputText(newValue.value);
      handleChange(newValue.value, "input");
    }
    setOpenDropdown(false);
    setIsTypingManually(false);
    inputRef.current?.blur();
  };

  const handleSubmit = (message: string, reason: string) => {
    if (!message.trim()) return;
    handleChange(message, reason);
    setInputText("");
    setInputValue("");
    setSubmitDisabled(true);
    setIsTypingManually(false);
    setTimeout(() => setSubmitDisabled(false), 500);
  };

  const handleIconClick = () => {
    if (inputText.trim()) {
      handleSubmit(inputText, "input");
    }
  };

  const handleBlur = () => {
    setOpenDropdown(false);
    setIsTypingManually(false);
    setHoveredOptionIndex(null);
    if (!inputText && !inputValue) {
      setInputText("");
      setInputValue("");
      onCloseDropdown();
    }
  };

  const handleFocus = () => {
    setIsTypingManually(true);
  };

  const commonInputStyles = {
    cursor: isDisableTextQueryInProd ? 'default' : 'pointer',
    minHeight: "64px",
    padding: "20px",
    paddingRight: "60px",
    backgroundColor: theme.elevation.paperElevationTwo,
    border: "none",
    color: isDisableTextQueryInProd ? theme.palette.text.disabled : theme.palette.text.primary,
    WebkitTextFillColor: isDisableTextQueryInProd ? theme.palette.text.disabled : undefined,
    caretColor: isDisableTextQueryInProd ? 'transparent' : undefined,
  };

  const resetInputAndDropdownState = () => {
    setOpenDropdown(false);
    setInputText("");
    setInputValue("");
    onCloseDropdown();
  };

  const handleInputClick = () => {
    resetInputAndDropdownState();
  };

  const handleKeyDown = (event: React.KeyboardEvent) => {
    if (event.key === "Backspace" && inputText === "") {
      resetInputAndDropdownState();
    } else if (event.key === "Escape") {
      event.preventDefault();
      resetInputAndDropdownState();
      setHoveredOptionIndex(null);
      inputRef.current?.blur();
    } else if (event.key === "Enter") {
      event.preventDefault();
      if (!submitDisabled && inputText.trim()) {
        handleSubmit(inputText, "input");
      }
    }
  };

  return (
    <FormControl fullWidth>
      <Box sx={{ position: "relative" }}>
        {centeredContent ? (
          <Autocomplete
            id="prompt-combo"
            freeSolo
            options={tagsData}
            getOptionLabel={(option) => option.label || ""}
            open={openDropdown}
            inputValue={inputValue}
            onInputChange={handleInputChange}
            onChange={handleChangeDropdown}
            onClose={() => {
              setTagsData([]);
            }}
            onBlur={handleBlur}
            PaperComponent={({ children }) => (
              <Paper
                elevation={0}
                style={{
                  background: theme.palette.background.default,
                  marginLeft: "16px",
                  marginRight: "16px",
                  width: `calc(100% - ${2 * 16}px)`,
                  marginTop: isMobileOrTablet ? "0px" : "8px",
                }}
              >
                {children}
              </Paper>
            )}
            renderInput={(params) => (
              <TextField
                {...params}
                fullWidth
                multiline
                maxRows={6}
                value={inputText}
                onClick={handleInputClick}
                disabled={loadingState || isDisableTextQueryInProd}
                sx={{
                  "& .MuiInputBase-input::placeholder": {
                    color: theme.palette.text.secondary,
                    opacity: 1,
                  },
                  "& .MuiOutlinedInput-root": {
                    borderRadius: isMultiline ? "20px" : "40px",
                    transition: "border-radius 0.3s ease",
                    display: "flex",
                    alignItems: "center",
                    p: "20px !important",
                    pr: "60px !important",
                    "& fieldset": {
                      borderBottom: "none",
                      borderColor: "transparent !important",
                      textAlign: "center",
                    },
                  },
                  "& .MuiAutocomplete-input": {
                    p: "0px !important",
                  },
                }}
                InputProps={{
                  ...params.InputProps,
                  endAdornment: null,
                  style: commonInputStyles,
                }}
                onKeyDown={handleKeyDown}
                inputRef={inputRef}
                placeholder={centeredContent && labelText}
                onFocus={handleFocus}
              />
            )}
            clearOnEscape={false}
            clearOnBlur={false}
            renderOption={(props, option, { index }) => (
              <Box
                {...props}
                onMouseEnter={(event) => {
                  props.onMouseEnter?.(event);
                  setHoveredOptionIndex(index);
                }}
                onMouseLeave={(event) => {
                  props.onMouseLeave?.(event);
                  setHoveredOptionIndex(null);
                }}
                sx={{
                  height: isMobile ? "auto" : "50px",
                  display: "flex",
                  alignItems: "center",
                  borderRadius: "16px",
                  px: 3,
                  transition: "background-color 0.2s ease",
                  position: 'relative',
                  overflow: 'hidden',
                  backgroundColor: theme.palette.background.default,

                  "&:hover, &.Mui-focused": {
                    backgroundColor: theme.elevation.paperElevationTwo,
                  },
                  "&.MuiAutocomplete-option.Mui-focused": {
                    backgroundColor: theme.elevation.paperElevationTwo,
                  },
                  ...(index === tagsData.length - 1 && {
                    borderBottomLeftRadius: "16px",
                    borderBottomRightRadius: "16px",
                  }),

                  "&::after": {
                    content: '""',
                    position: 'absolute',
                    bottom: 0,
                    left: 0,
                    right: 0,
                    height: '1px',
                    backgroundColor: theme.elevation.paperElevationTwo,
                    display: (index < tagsData.length - 1 && index !== hoveredOptionIndex && index !== hoveredOptionIndex - 1) ? 'block' : 'none',
                    opacity: 1,
                    transition: 'opacity 0.2s ease',
                  },
                  "&:hover::after, &.Mui-focused::after": {
                    opacity: 0,
                    backgroundColor: theme.elevation.paperElevationTwo
                  },
                }}
              >
                {option.label}
              </Box>
            )}
          />
        ) : (
          <TextField
            fullWidth
            multiline
            maxRows={6}
            value={inputText}
            onChange={handleInputChange}
            disabled={loadingState || isDisableTextQueryInProd}
            label=""
            sx={{
              "& .MuiInputBase-input::placeholder": {
                color: theme.palette.text.secondary,
                opacity: 1,
              },
              "& .MuiOutlinedInput-root": {
                borderRadius: isMultiline ? "20px" : "40px",
                transition: "border-radius 0.3s ease",
                display: "flex",
                alignItems: "center",
                "& fieldset": {
                  borderColor: "transparent !important",
                  textAlign: "center",
                  border: "none",
                },
              },
            }}
            InputProps={{
              style: commonInputStyles,
            }}
            onKeyDown={handleKeyDown}
            inputRef={inputRef}
            placeholder={labelText}
          />
        )}
        <IconButton
          aria-label="Submit"
          onClick={handleIconClick}
          disabled={loadingState || !inputText.trim()}
          sx={{
            position: "absolute",
            right: "20px",
            bottom: "16px",
            borderRadius: "50%",
            backgroundColor: inputText
              ? theme.palette.primary.main
              : theme.elevation.paperElevationSixteen,
            "&:hover": {
              backgroundColor: inputText
                ? theme.palette.primary.main
                : theme.elevation.paperElevationSixteen,
            },
            "&.Mui-disabled": {
              backgroundColor: theme.elevation.paperElevationSixteen,
              color: theme.palette.action.disabledBackground,
            },
          }}
        >
          <Box
            sx={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
              width: "100%",
              height: "100%",
              borderRadius: "50%",
            }}
          >
            <ThickArrowRightIcon width={16} height={16} />
          </Box>
        </IconButton>
      </Box>
    </FormControl>
  );
};

export default PromptControl;