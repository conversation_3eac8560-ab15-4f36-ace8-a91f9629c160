#!/bin/bash

# Get script directory for relative imports
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Source shared libraries
source "$SCRIPT_DIR/lib/common.sh"
source "$SCRIPT_DIR/lib/gcloud.sh"

# Initialize script with strict error handling
init_script

# Parse command line arguments
DRY_RUN=false
while [[ $# -gt 0 ]]; do
    case $1 in
        --dry-run)
            DRY_RUN=true
            shift
            ;;
        -h|--help)
            show_help_header "$0" "Fetch environment secrets from Google Cloud Secret Manager and convert to YAML format"
            echo "Environment Variables:"
            echo "  TARGET       Required. Must be one of: development, testing, production"
            echo "  SERVICE      Required. Service name for secret identification"
            echo ""
            echo "Notes:"
            echo "  - When TARGET=testing, secrets are pulled from production"
            echo "  - Output file: ${SCRIPT_DIR}/env.\${SERVICE}.tmp.yml"
            show_help_footer
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            echo "Use --help for usage information"
            exit 1
            ;;
    esac
done

# Configuration
readonly OUTPUT_ENV_FILE="${SCRIPT_DIR}/env.${SERVICE}.tmp.yml"

# Custom cleanup function for this script
env_cleanup() {
    local exit_code=$?
    if [ $exit_code -ne 0 ]; then
        log_error "Script failed with exit code $exit_code"
        if [ "$DRY_RUN" != true ] && [ -f "$OUTPUT_ENV_FILE" ]; then
            log_info "Cleaning up incomplete env file: $OUTPUT_ENV_FILE"
            rm -f "$OUTPUT_ENV_FILE"
        fi
    fi
    exit $exit_code
}

# Set up cleanup trap
setup_cleanup env_cleanup

# Function to validate environment variables
validate_environment() {
    log_info "Validating environment variables..."
    
    require_env_var TARGET "TARGET" || exit 1
    require_env_var SERVICE "SERVICE" || exit 1
    
    # shellcheck disable=SC2153
    validate_enum "$TARGET" "TARGET" "development" "testing" "production" || exit 1
    
    log_success "Environment variables validated"
}

# Function to determine secret name based on target
get_secret_name() {
    local target="$1"
    local service="$2"
    
    # For testing, use production secrets
    if [[ "$target" == "testing" ]]; then
        echo "impactai-$service-production-env-file"
    else
        echo "impactai-$service-$target-env-file"
    fi
}

# Function to fetch secrets from Google Cloud Secret Manager
fetch_secrets() {
    local target="$1"
    local service="$2"
    local secret_name
    local secret_content
    
    secret_name=$(get_secret_name "$target" "$service")
    
    if [ "$DRY_RUN" = true ]; then
        log_info "[DRY RUN] Would fetch secret: $secret_name" >&2
        log_success "[DRY RUN] Secret fetch validation completed" >&2
        return 0
    fi
    
    log_info "Fetching secret: $secret_name" >&2
    
    # Capture the secret content separately from logging
    if ! secret_content=$(gcloud secrets versions access latest --secret="$secret_name" 2>/dev/null); then
        log_error "Failed to fetch secret: $secret_name" >&2
        log_info "Please verify that:" >&2
        log_info "  - The secret exists in Google Cloud Secret Manager" >&2
        log_info "  - You have the necessary permissions to access it" >&2
        log_info "  - The SERVICE variable is set correctly" >&2
        exit 1
    fi
    
    log_success "Secret fetched successfully" >&2
    
    # Output only the secret content to stdout
    echo "$secret_content"
}

# Function to convert secrets to YAML format
convert_to_yaml() {
    local secret_content="$1"
    
    if [ "$DRY_RUN" = true ]; then
        log_info "[DRY RUN] Would convert secrets to YAML format"
        log_info "[DRY RUN] Output file would be: $OUTPUT_ENV_FILE"
        log_success "[DRY RUN] YAML conversion validation completed"
        return 0
    fi
    
    log_info "Converting secrets to YAML format..."
    log_info "Output file: $OUTPUT_ENV_FILE"
    
    # Ensure the output directory exists
    local output_dir
    output_dir=$(dirname "$OUTPUT_ENV_FILE")
    if [[ ! -d "$output_dir" ]]; then
        log_info "Creating output directory: $output_dir"
        mkdir -p "$output_dir" || {
            log_error "Failed to create output directory: $output_dir"
            exit 1
        }
    fi

    # Clear the output file if it exists
    true > "$OUTPUT_ENV_FILE" || {
        log_error "Failed to create/clear output file: $OUTPUT_ENV_FILE"
        exit 1
    }

    local line_count=0
    local processed_count=0

    # Parse the KEY=VALUE format and convert to simple YAML format for Cloud Run
    while IFS= read -r line; do
        line_count=$((line_count + 1))
        
        # Skip empty lines
        if [[ -n "$line" ]]; then
            if [[ $line =~ ^([^=]+)=(.*)$ ]]; then
                local env_name="${BASH_REMATCH[1]}"
                local env_value="${BASH_REMATCH[2]}"
                
                # Escape double quotes in the value for proper YAML formatting
                env_value="${env_value//\"/\\\"}"
                
                echo "$env_name: \"$env_value\"" >> "$OUTPUT_ENV_FILE"
                processed_count=$((processed_count + 1))
            else
                log_warning "Skipping malformed line $line_count: $line"
            fi
        fi
    done <<< "$secret_content"
    
    if [ $processed_count -eq 0 ]; then
        log_error "No valid environment variables found in secret"
        log_info "Expected format: KEY=VALUE (one per line)"
        exit 1
    fi
    
    log_success "Converted $processed_count environment variables from $line_count lines"
}

# Function to validate output file
validate_output() {
    if [ "$DRY_RUN" = true ]; then
        log_info "[DRY RUN] Would validate output file"
        log_success "[DRY RUN] Output validation completed"
        return 0
    fi
    
    if [[ ! -f "$OUTPUT_ENV_FILE" ]]; then
        log_error "Output file was not created: $OUTPUT_ENV_FILE"
        exit 1
    fi
    
    local file_size
    file_size=$(wc -l < "$OUTPUT_ENV_FILE")
    
    if [ "$file_size" -eq 0 ]; then
        log_error "Output file is empty: $OUTPUT_ENV_FILE"
        exit 1
    fi
    
    log_success "Output file validated: $file_size lines written to $OUTPUT_ENV_FILE"
}

# Main execution
main() {
    if [ "$DRY_RUN" = true ]; then
        log_info "Starting environment configuration validation (DRY RUN MODE)"
    else
        log_info "Starting environment configuration generation"
    fi
    
    log_info "Target: ${TARGET:-'<not set>'}"
    log_info "Service: ${SERVICE:-'<not set>'}"
    
    # If testing target, show which secrets will be used
    if [[ "${TARGET:-}" == "testing" ]]; then
        log_info "Note: Testing target will use production secrets"
    fi
    
    # Validation and pre-flight checks
    validate_environment
    check_gcloud_auth
    
    # Fetch secrets
    local secret_content
    secret_content=$(fetch_secrets "$TARGET" "$SERVICE")

    # Convert to YAML format
    convert_to_yaml "$secret_content"

    # Validate output
    validate_output
    
    if [ "$DRY_RUN" = true ]; then
        log_success "Environment configuration validation completed successfully!"
    else
        log_success "Environment configuration generated successfully!"
    fi
}

# Run main function
main "$@"
