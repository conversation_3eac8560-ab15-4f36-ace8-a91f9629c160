import React, { ReactNode, useState, useEffect, useCallback } from 'react';
import Tooltip, { TooltipProps } from '@mui/material/Tooltip';
import Typography from '@mui/material/Typography';
import { useTheme } from '@mui/material/styles';
import Box from '@mui/material/Box';

interface CustomTooltipProps {
  children: ReactNode;
  content: ReactNode;
  variant?: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6' | 'subtitle1' | 'subtitle2' | 'body1' | 'body2' | 'caption' | 'button' | 'overline' | 'inherit';
  fontSize?: string | number;
  disableOnScroll?: boolean;
  scrollContainer?: string; // ID of the scroll container element
  placement?: TooltipProps['placement'];
  arrow?: boolean;
}

const CustomTooltip: React.FC<CustomTooltipProps> = ({
  children,
  content,
  variant = 'caption',
  fontSize = '12px',
  disableOnScroll = false,
  scrollContainer = 'sidenav-infinite-scroll',
  placement = 'right',
  arrow = false,
  ...rest
}) => {
  const theme = useTheme();
  const [open, setOpen] = useState(false);
  const [isScrolling, setIsScrolling] = useState(false);
  const [scrollTimer, setScrollTimer] = useState<number | null>(null);

  const handleScroll = useCallback(() => {
    if (!disableOnScroll) return;

    setIsScrolling(true);
    setOpen(false);

    if (scrollTimer) {
      window.clearTimeout(scrollTimer);
    }

    const timerId = window.setTimeout(() => {
      setIsScrolling(false);
    }, 0);
    
    setScrollTimer(timerId as unknown as number);
  }, [disableOnScroll, scrollTimer]);

  useEffect(() => {
    if (!disableOnScroll) return;

    const scrollElement = document.getElementById(scrollContainer);
    if (scrollElement) {
      scrollElement.addEventListener('scroll', handleScroll);
      
      return () => {
        scrollElement.removeEventListener('scroll', handleScroll);
        if (scrollTimer) {
          window.clearTimeout(scrollTimer);
        }
      };
    }
  }, [disableOnScroll, scrollContainer, handleScroll]);

  const handleTooltipClose = () => {
    setOpen(false);
  };

  const handleTooltipOpen = () => {
    if (!isScrolling) {
      setOpen(true);
    }
  };

  const renderChildren = () => {
    if (React.isValidElement(children)) {
      return React.cloneElement(children);
    }
    return <Box component="span">{children}</Box>;
  };

  return (
    <Tooltip
      {...rest}
      open={disableOnScroll ? open : undefined}
      onClose={disableOnScroll ? handleTooltipClose : undefined}
      onOpen={disableOnScroll ? handleTooltipOpen : undefined}
      placement={placement}
      arrow={arrow}
      slotProps={{
        tooltip: {
          sx: {
            borderRadius: '4px',
            backgroundColor: theme.palette.grey[800],
            border: 'none',
            maxWidth: 200,
          },
        },
      }}
      title={
        <Typography
          variant={variant}
          sx={{
            color: theme.palette.common.white,
            border: 'none',
            whiteSpace: 'normal',
            fontSize: fontSize,
            fontFamily: "Roboto",
          }}
        >
          {content}
        </Typography>
      }
    >
      {renderChildren()}
    </Tooltip>
  );
};

export default CustomTooltip;