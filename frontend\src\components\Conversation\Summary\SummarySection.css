.markdown-container>* {
    line-height: 1.75;
    text-justify: inter-word;
    text-align-last: auto;
    text-decoration: none;
}

.markdown-container > :first-child {
    margin-top: 0 !important;
}

.markdown-container > :last-child {
    margin-bottom: 0 !important;
}

.markdown-container {
    text-align: justify !important;
    overflow-wrap: break-word;
    hyphens: auto;
    max-width: 100%;
    white-space: normal;
    -webkit-hyphens: auto;
    text-justify: distribute;
    text-align-last: left;
    word-spacing: -0.3px;
    text-decoration: none;
}

.markdown-container em {
    font-style: normal;
    font-weight: inherit;
    text-decoration: none;
    color: inherit;
}

.markdown-container ul {
    padding-left: 15px;
}

.graph-sticky-element {
    position: sticky;
    top: 0px;
    transition: top 0.3s ease-in-out, transform 0.3s ease-in-out;
}

.animated-word {
    opacity: 0;
    animation: fadeIn 0.05s forwards;
}


@keyframes fadeIn {
    to {
        opacity: 1;
    }
}

.markdown-container hr:last-of-type {
    border: none;
    background-color: transparent;
    height: 0;
    margin-top: 8px;
}