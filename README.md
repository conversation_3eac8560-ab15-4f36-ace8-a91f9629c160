# ImpactAI Documentation

Welcome to the ImpactAI project documentation. This repository contains a comprehensive AI-powered platform for development economics research analysis and insights.

## Overview

ImpactAI is a specialized platform designed for development practitioners, policymakers, researchers, and international organizations. It provides research-backed insights using customized large language models (LLMs) and a structured knowledge database of validated research studies.

### Key Features

- AI-powered research analysis
- Interactive visualization of policy interventions
- Curated knowledge database of validated research
- Structured information on policy interventions and outcomes
- Quality-assessed research findings
- Bias mitigation through structured prompting

### Repository Structure

The repository is organized into several main components:

- [`backend/`](./backend.md) - Main application backend API
- [`agent/`](../docs/agent/README.md) - AI agent for processing and analyzing research papers
- [`frontend/`](./frontend.md) - Application frontend interface
- [`website/`](./website.md) - Landing page and marketing website
- [`infrastructure/`](./infrastructure.md) - Deployment and infrastructure configuration
- [`proxy/`](./proxy.md) - Proxy service configuration

## Getting Started

1. Install Docker following the [official installation guide](https://docs.docker.com/engine/install/)

2. Clone the repository:
   ```bash
   git clone https://github.com/worldbank/causal-ai-product.git
   cd causal-ai-product
   ```

3. Start the development environment:

- If you want to develop for the frontend
   ```bash
   make up
   ```
   You can also just `cd frontend` and `npm run dev` to start frontend development without docker

- If you want to develop for the backend
   ```bash
   make up-backend
   ```

- If you want to develop for the agent
   ```bash
   make up-agent
   ```

### Access Points

The application provides the following services (defined in `.honcho.yml`):

- **Frontend**: Web application interface
- **Backend**: API server with documentation
- **Agent**: AI processing service
- **Caddy**: Reverse proxy for domain routing

For local development URLs, see the service configuration in `services.yml` and the service map in `infrastructure/service-map.sh`.

To stop the development environment:
```bash
make down
```

## Platform Features

### Research Source Information
ImpactAI provides rich research source data including:
- **Full Paper Abstracts**: Complete abstract text retrieved directly from database
- **Comprehensive Metadata**: Detailed research context including quality scores
- **High Performance**: Optimized database connection pooling (20 concurrent connections)
- **Rich User Experience**: Informative research context for development practitioners

### Advanced Text Processing
ImpactAI includes sophisticated text processing capabilities for clean, contextual research summaries:
- **Intelligent Sanitization**: Automated text cleaning and formatting for research content
- **Data-Driven Filtering**: Intervention-outcome pairs validated against available data
- **Context Awareness**: Removes irrelevant references when no supporting data is available
- **Source Citation Filtering**: Validates source references like `[A123]`, `[P456]` against paper datasets
- **Multi-Layer Processing**: Comprehensive text cleaning through integrated processing pipeline
- **Professional Presentation**: Clean, formatted text optimized for development practitioners

### Quality Assurance
The enhanced text processing system ensures:
- **Citation Integrity**: Only sources with supporting data appear in summaries
- **Evidence-Based Research**: All citations backed by actual research data
- **Professional Quality**: Publication-ready text suitable for policy documents
- **Consistent Formatting**: Standardized text presentation across all responses

For technical details, see [API Documentation](./backend/docs/api-enhancements.md).

## Additional Documentation

- [Backend documentation](./backend/README.md)
- [Agent documentation](./agent/README.md)
- [Frontend documentation](./frontend/README.md)
- [Website documentation](./website/README.md)
- [Infrastructure documentation](./docs/infrastructure.md)
- [API Documentation](./backend/docs/api-enhancements.md)
- [Architecture overview](./docs/architecture.md)
- [Development guide](./docs/development.md)
