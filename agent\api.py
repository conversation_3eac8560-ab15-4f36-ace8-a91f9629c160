import structlog
import os
import logging
from typing import Optional
from contextlib import asynccontextmanager
from pydantic import BaseModel
from structlog.processors import TimeStamper
from fastapi import FastAP<PERSON>, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse, HTMLResponse
from src.services.agent import agent_service
from src.utils.db import engine
from fastapi.responses import StreamingResponse
from src.services.cache import destroy_redis_connection_pool, initialize_redis_connection_pool

DEBUG = os.environ.get("DEBUG", True)

structlog.configure(
    processors=[
        TimeStamper(fmt="iso"),
        structlog.processors.JSONRenderer(),
    ],
    logger_factory=structlog.stdlib.LoggerFactory(),
    wrapper_class=structlog.stdlib.BoundLogger,
    cache_logger_on_first_use=True,
)

logging.basicConfig(
    level=logging.INFO,
    format="%(message)s",
)

logger = logging.getLogger(__name__)


async def on_startup():
    logger.info("Starting agent service")
    initialize_redis_connection_pool()
    engine.connect()


async def on_shutdown():
    logger.info("Shutting down agent service")
    destroy_redis_connection_pool()


@asynccontextmanager
async def lifespan(_app: FastAPI):
    await on_startup()
    yield
    await on_shutdown()


app = FastAPI(debug=DEBUG, lifespan=lifespan)

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


class AgentExecuteBody(BaseModel):
    conversation_id: Optional[str]
    query: str


@app.get("/", response_class=HTMLResponse)
def get_root():
    current_dir = os.path.dirname(os.path.abspath(__file__))
    preview_path = os.path.join(current_dir, "preview.html")

    with open(preview_path, "r", encoding="utf-8") as f:
        return HTMLResponse(content=f.read())


@app.post("/execute")
async def execute(body: AgentExecuteBody):
    conversation_id = body.conversation_id
    response = await agent_service.execute(conversation_id, body.query)

    return JSONResponse(
        status_code=status.HTTP_200_OK,
        content={"response": response},
    )


@app.get("/conversations/{conversation_id}/messages/{message_id}/summary")
async def get_conversation_message_summary(
    conversation_id: str, message_id: str, query: str
):
    return StreamingResponse(
        agent_service.generate_summary(conversation_id, message_id, query),
        media_type="text/event-stream"
    )

@app.get("/conversations/{conversation_id}/messages/{message_id}/data")
async def get_conversation_message_data(conversation_id: str, message_id: str):
    message_data = await agent_service.generated_summary_data(conversation_id, message_id)
    return JSONResponse(
        status_code=status.HTTP_200_OK,
        content=message_data,
    )

@app.get("/health")
async def health_check():
    try:
        return JSONResponse(
            status_code=status.HTTP_200_OK, content={"status": "healthy!"}
        )
    except Exception as e:
        return JSONResponse(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            content={"status": "unhealthy", "error": str(e)},
        )
