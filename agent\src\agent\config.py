"""Configuration for the agent."""

from typing import Dict, Any, Optional
from pydantic import BaseModel, SecretStr
import os
import dotenv

dotenv.load_dotenv()


class DatasetSettings(BaseModel):
    """Configuration for the dataset."""

    bucket_name: str = "impactai-tmp-agent-dataset"
    credentials_path: Optional[str] = os.getenv("GOOGLE_APPLICATION_CREDENTIALS")


class AgentConfig(BaseModel):
    """Configuration for the agent."""

    # Vertex AI settings
    project_id: str = "impactai-430615"
    location: str = "us-east1"
    model_name: str = "gemini-2.0-flash-001"

    # Agent settings
    max_iterations: int = 5
    temperature: float = 0.3
    max_tokens: int = 8192

    # Tool settings
    google_api_key: Optional[SecretStr] = None
    database_url: Optional[str] = None
    bucket_name: Optional[str] = None
    use_gcp: bool = False

    # Optional settings
    debug: bool = False
    verbose: bool = False

    def get(self, key: str, default: Any = None) -> Any:
        """Get config value with fallback to default."""
        return getattr(self, key, default)

    def __getitem__(self, key: str) -> Any:
        """Allow dictionary-style access to config."""
        return getattr(self, key)

    @classmethod
    def from_dict(cls, config: Dict[str, Any]) -> "AgentConfig":
        """Create config from dictionary."""
        # Map old config keys to new ones
        key_mapping = {
            "model_name": "model_name",
            "google_api_key": "google_api_key",
            "database_url": "database_url",
        }

        # Convert old keys to new ones
        processed_config = {}
        for key, value in config.items():
            new_key = key_mapping.get(key, key)
            processed_config[new_key] = value

        # Set google_api_key from environment if not provided
        if "google_api_key" not in processed_config:
            api_key = os.getenv("GOOGLE_API_KEY")
            if api_key:
                processed_config["google_api_key"] = SecretStr(api_key)

        return cls(**processed_config)

    def to_dict(self) -> Dict[str, Any]:
        """Convert config to dictionary."""
        return self.dict(exclude_none=True)


class DatabaseConfig(BaseModel):
    """Configuration class for the database."""

    url: str
    schema_path: str
    max_connections: int = 5
    timeout: int = 30
