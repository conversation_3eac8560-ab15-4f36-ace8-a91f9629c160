"""Entity extraction module for development economics research."""

import json
import logging
from typing import Any, Dict, List

import aiohttp
from pydantic import BaseModel

from src.tools.base import Tool

logger = logging.getLogger(__name__)

# API Configuration
ENTITY_API_URL = "https://proxy.impact-ai-dev.app/entity-linking"


class EntityMention(BaseModel):
    """Entity with ID and label."""

    id: list[str] | list[int]
    label: str
    mention: str = ""
    short_label: str = ""


class ExtractedEntities(BaseModel):
    """Extracted entities from a research user query matching the API response structure."""

    user_query: str
    country_codes: List[str] = []
    interventions: List[EntityMention] = []
    outcomes: List[EntityMention] = []
    intervention_sectors: List[EntityMention] = []
    intervention_target_populations: List[EntityMention] = []
    outcome_sectors: List[EntityMention] = []
    outcome_target_populations: List[EntityMention] = []

    extended_interventions: List[EntityMention] = []
    extended_outcomes: List[EntityMention] = []
    extended_intervention_sectors: List[EntityMention] = []
    extended_intervention_target_populations: List[EntityMention] = []
    extended_outcome_sectors: List[EntityMention] = []
    extended_outcome_target_populations: List[EntityMention] = []

    # Define entity fields for iteration
    _ENTITY_FIELDS = [
        "interventions",
        "outcomes",
        "intervention_sectors",
        "intervention_target_populations",
        "outcome_sectors",
        "outcome_target_populations",
        "extended_interventions",
        "extended_outcomes",
        "extended_intervention_sectors",
        "extended_intervention_target_populations",
        "extended_outcome_sectors",
        "extended_outcome_target_populations",
    ]

    # Field display names for better readability
    _FIELD_LABELS = {
        "interventions": "Interventions",
        "outcomes": "Outcomes",
        "intervention_sectors": "Intervention Sectors",
        "intervention_target_populations": "Intervention Target Populations",
        "outcome_sectors": "Outcome Sectors",
        "outcome_target_populations": "Outcome Target Populations",
        "extended_interventions": "Extended Interventions",
        "extended_outcomes": "Extended Outcomes",
        "extended_intervention_sectors": "Extended Intervention Sectors",
        "extended_intervention_target_populations": "Extended Intervention Target Populations",
        "extended_outcome_sectors": "Extended Outcome Sectors",
        "extended_outcome_target_populations": "Extended Outcome Target Populations",
    }

    def _entity_to_dict(self, entity: EntityMention) -> Dict[str, Any]:
        """Convert an EntityMention to dictionary format."""
        return {
            "id": entity.id,
            "label": entity.label,
            "mention": entity.mention,
            "short_label": entity.short_label,
        }

    def to_dict(self) -> Dict[str, Any]:
        """Convert the payload to the API-expected dictionary format."""
        result = {
            "user_query": self.user_query,
            "country_codes": self.country_codes,
        }

        # Add all entity fields
        for field in self._ENTITY_FIELDS:
            entities = getattr(self, field)
            if field in ["interventions", "outcomes"]:
                # Special format for interventions and outcomes
                result[field] = [self._entity_to_dict(entity) for entity in entities]
            else:
                # Standard format for other fields
                result[field] = [vars(entity) for entity in entities]

        return result

    def _format_entities(self, entities: List[EntityMention]) -> str:
        """Format a list of entities for string representation."""
        return ", ".join([str(self._entity_to_dict(entity)) for entity in entities])

    def __str__(self) -> str:
        """String representation of the user query and the extracted entities."""
        parts = [f"\nuser_query: {self.user_query}"]

        # Add countries if present
        if self.country_codes:
            parts.append(f"\nCountries: {', '.join(self.country_codes)}")

        # Add all entity fields that have content
        for field in self._ENTITY_FIELDS:
            entities = getattr(self, field)
            if entities:
                label = self._FIELD_LABELS[field]
                formatted_entities = self._format_entities(entities)
                parts.append(f"\n{label}: {formatted_entities}")

        return "\n".join(
            [
                "We have just executed the EntityExtractor tool and retrieved the following entities:",
                "Output: " + "; ".join(parts),
            ]
        )


class EntityExtractor(Tool):
    """Tool for extracting research entities from queries using external API."""

    # Define entity field mappings for processing
    _ENTITY_FIELDS = [
        "interventions",
        "outcomes",
        "intervention_sectors",
        "intervention_target_populations",
        "outcome_sectors",
        "outcome_target_populations",
    ]

    def __init__(self, config: Dict[str, Any]):
        """Initialize the entity extractor."""
        super().__init__(
            name="entity_extractor",
            description="Extract key research entities from queries including countries, interventions, outcomes, sectors, and target populations to narrow down the search for relevant research papers",
            func=self.extract,
            arguments=[("user_query", "str")],
            outputs=[
                (
                    "entities",
                    "ExtractedEntities(user_query: str, country_codes: List[str], interventions: List[EntityMention], outcomes: List[EntityMention], intervention_sectors: List[EntityMention], intervention_target_populations: List[EntityMention], outcome_sectors: List[EntityMention], outcome_target_populations: List[EntityMention], extended_interventions: List[EntityMention], extended_outcomes: List[EntityMention], extended_intervention_sectors: List[EntityMention], extended_intervention_target_populations: List[EntityMention], extended_outcome_sectors: List[EntityMention], extended_outcome_target_populations: List[EntityMention])",
                )
            ],
            config=config,
        )
        self.verbose = config.get("verbose", False)
        self.session = config.get("session")
        self.owns_session = False

    async def _get_session(self):
        """Get a valid session for API calls."""
        if self.session is not None and not self.session.closed:
            return self.session

        self.session = aiohttp.ClientSession()
        self.owns_session = True
        return self.session

    async def _call_entity_api(self, user_query: str) -> Dict[str, Any]:
        """Call the entity linking API."""
        try:
            session = await self._get_session()
            async with session.post(
                ENTITY_API_URL,
                json={"user_text": user_query},
                headers={"Content-Type": "application/json"},
            ) as response:
                response.raise_for_status()
                return await response.json()
        except Exception as e:
            logger.error(f"Error calling entity API: {e}")
            raise ValueError(f"Entity API request failed: {e}")

    def _create_entity_mention(self, item: Dict[str, Any]) -> EntityMention:
        """Create an EntityMention from API response item."""
        return EntityMention(
            id=[item["id"]] + item.get("merged_ids", []),
            label=item["label"],
            mention=item.get("mention", ""),
            short_label=item.get("short_label", ""),
        )

    def _extract_extended_entities(
        self, api_response: Dict[str, Any]
    ) -> Dict[str, List[EntityMention]]:
        """Extract all extended entities from closest_group fields."""
        extended_entities = {}

        for field in self._ENTITY_FIELDS:
            extended_field = f"extended_{field}"
            extended_entities[extended_field] = []

            for item in api_response.get(field, []):
                if "closest_group" in item:
                    for group_item in item["closest_group"]:
                        extended_entities[extended_field].append(
                            EntityMention(
                                id=[group_item["id"]],
                                label=group_item["label"],
                                short_label=group_item["short_label"],
                            )
                        )

        return extended_entities

    def _extract_regular_entities(
        self, api_response: Dict[str, Any]
    ) -> Dict[str, List[EntityMention]]:
        """Extract all regular entities from API response."""
        regular_entities = {}

        for field in self._ENTITY_FIELDS:
            regular_entities[field] = [
                self._create_entity_mention(item)
                for item in api_response.get(field, [])
            ]

        return regular_entities

    def _convert_api_response(
        self, api_response: Dict[str, Any], user_query: str
    ) -> ExtractedEntities:
        """Convert API response to ExtractedEntities object."""
        try:
            # Extract extended and regular entities
            extended_entities = self._extract_extended_entities(api_response)
            regular_entities = self._extract_regular_entities(api_response)

            # Combine all entities into response
            mapped_response = {
                "user_query": user_query,
                "country_codes": api_response.get("country_codes", []),
                **regular_entities,
                **extended_entities,
            }

            return ExtractedEntities(**mapped_response)

        except Exception as e:
            logger.error(f"Error converting API response: {e}")
            raise ValueError(f"Failed to convert API response: {e}")

    async def extract(self, user_query: str) -> ExtractedEntities:
        """Extract entities from a user query using the external API."""
        try:
            if self.verbose:
                logger.info(f"Extracting entities for user query: {user_query}")

            # Call API and convert response
            api_response = await self._call_entity_api(user_query)

            if self.verbose:
                logger.info(f"API Response: {json.dumps(api_response, indent=4)}")

            entities = self._convert_api_response(api_response, user_query)

            if self.verbose:
                logger.info(f"Extracted entities: {entities}")

            return entities

        except Exception as e:
            logger.error(f"Error extracting entities: {str(e)}")
            # Return empty entities on error
            return ExtractedEntities(user_query=user_query)

    async def cleanup(self):
        """Clean up resources used by the tool."""
        if self.owns_session and self.session is not None and not self.session.closed:
            await self.session.close()
            self.session = None
