import React, { useEffect, useState, memo } from "react";
import {
  Box,
  Grid,
  Card,
  Typography,
  IconButton,
} from "@mui/material";
import { Source } from "../../../types/ConversationTypes";
import CloseIcon from "@mui/icons-material/Close";
import Sources from "../Sources/Sources";
import "./ComparisonView.css";
import * as d3 from "d3";
import { useIsMobile, useIsTablet } from "../../Layout/MobileUtils";
import EffectSizesPlot2 from "../Plot/Graph/EffectSizesPlot2";
import MeanEffectSizePlot from "../Plot/Graph/MeanEffectSizePlot";
import PairSelector from "../Plot/Graph/PairSelector";
import { PlotDataInfo } from "../../../types/ConversationTypes";

interface ActivePlotDetails {
  citation_ids: { key: string; value: string }[];
  messageId: string;
  plotDataInfo?: {
    data?: PlotDataInfo | { data: PlotDataInfo; type: string };
    title?: string;
  };
}

interface Outcome {
  outcome_tag_id: string;
  label: string;
}

interface ComparisonViewProps {
  informationId: string;
  sources: Source[] | undefined | null;
  theme: any;
  onClose: () => void;
  activePlotDetails: ActivePlotDetails | null;
  activeSourcePaperIds: string[];
  activeSourceMessageId: string | null;
  selectedStudy: string;
  onSelectStudy?: (id: string) => void;
}

const ComparisonView: React.FC<ComparisonViewProps> = ({
  informationId,
  sources,
  theme,
  onClose,
  activePlotDetails,
  activeSourcePaperIds,
  activeSourceMessageId,
  selectedStudy,
  onSelectStudy,
}) => {
  const [plotLoading, setPlotLoading] = useState(false);
  const [plotError, setPlotError] = useState<string | null>(null);
  const [plotData, setPlotData] = useState<any>({});
  const [showSources, setShowSources] = useState(false);
  const isMobile = useIsMobile();
  const isTablet = useIsTablet();
  const isMobileOrTablet = isMobile || isTablet;
  const [outcomeTagIds, setOutcomeTagIds] = useState('');
  const [interventionTagIds, setInterventionTagIds] = useState('');
  const [flatEffectSizes, setFlatEffectSizes] = useState<any[]>([]);
  const citationIds = activePlotDetails?.citation_ids || [];
  const messageIdFromPlot = activePlotDetails?.messageId || '';

  const handleCloseSourceCard = () => {
    if (onSelectStudy) {
      onSelectStudy('');
    }
  };

  function bisectArray<T>(array: T[], fn: (item: T) => boolean): [T[], T[]] {
    return array.reduce(
      (acc: [T[], T[]], val: T) => {
        acc[fn(val) ? 0 : 1].push(val);
        return acc;
      },
      [[], []]
    );
  }

  const topSelection = d3
    .groups(
      flatEffectSizes,
      (d) => `${d.outcome_tag_ids}_${d.intervention_tag_ids}`
    )
    .sort((a, b) => b[1].length - a[1].length);
  const bisected = bisectArray(
    topSelection,
    (d) => d[0] === `${outcomeTagIds}_${interventionTagIds}`
  );
  const top10 = [...bisected[0], ...bisected[1].slice(0, 9)];
  const top10Data = top10.map((d) => d[1]).flat();

  const uniqueOutcomes = top10Data
    .reduce((acc: Outcome[], cur) => {
      const outcomeTagId = cur.outcome_tag_ids;

      if (!acc.some((outcome) => outcome.outcome_tag_id === outcomeTagId)) {
        acc.push({
          outcome_tag_id: outcomeTagId,
          label: cur.outcome_tag_short_labels,
        });
      }
      return acc;
    }, [])
    .sort((a, b) => a.label.localeCompare(b.label));

  const [selectedOutcome2, setSelectedOutcome2] = useState<Outcome | undefined>(
    uniqueOutcomes.length > 0 ? uniqueOutcomes[0] : undefined
  );

  function getUniqueInterventions(selectedOutcome: Outcome | undefined) {
    if (!selectedOutcome) return [];
    return flatEffectSizes
      .filter((d) =>
        d.outcome_tag_ids.includes(
          +selectedOutcome?.outcome_tag_id || selectedOutcome.outcome_tag_id
        )
      )
      .reduce((acc: any[], cur) => {
        const interventionTagId = cur.intervention_tag_ids;

        if (
          !acc.some(
            (intervention) =>
              intervention.intervention_tag_id === interventionTagId
          )
        ) {
          acc.push({
            intervention_tag_id: interventionTagId,
            label: cur.intervention_tag_short_labels,
          });
        }
        return acc;
      }, [])
      .sort((a, b) => a.label[0].localeCompare(b.label[0]));
  }

  const uniqueInterventions = getUniqueInterventions(selectedOutcome2);

  const handleOnPairClicked = (pair: { intervention: string; outcome: string }) => {
    setInterventionTagIds(pair.intervention);
    setOutcomeTagIds(pair.outcome);
  };

  useEffect(() => {
    if (activePlotDetails) {
      const rawPlotData = activePlotDetails?.plotDataInfo?.data;
      const resolvedPlotData = (rawPlotData && 'data' in rawPlotData && rawPlotData.data) ? rawPlotData.data : rawPlotData;
      setPlotData(resolvedPlotData);
      setFlatEffectSizes(resolvedPlotData?.flat_effect_sizes || []);

      if (activePlotDetails.citation_ids?.[0]?.value && activePlotDetails.citation_ids?.[1]?.value) {
        setInterventionTagIds(activePlotDetails.citation_ids[0].value);
        setOutcomeTagIds(activePlotDetails.citation_ids[1].value);
      } else if (resolvedPlotData?.flat_effect_sizes?.length > 0) {
        const firstPair = d3
          .groups(
            resolvedPlotData.flat_effect_sizes,
            (d) => `${d.outcome_tag_ids}_${d.intervention_tag_ids}`
          )
          .sort((a, b) => b[1].length - a[1].length)?.[0]?.[1]?.[0];

        if (firstPair) {
          setInterventionTagIds(firstPair.intervention_tag_ids);
          setOutcomeTagIds(firstPair.outcome_tag_ids);
        }
      }
    } else {
      setPlotData(null);
      setFlatEffectSizes([]);
      setInterventionTagIds('');
      setOutcomeTagIds('');
    }
  }, [activePlotDetails]);

  const getInterventionDetails = () => {
    const matchingEffectSize = flatEffectSizes.find(
      d => d.intervention_tag_ids.includes(interventionTagIds) &&
        d.outcome_tag_ids.includes(outcomeTagIds) &&
        (String(d.id) === String(selectedStudy) || String(d.paper_id) === String(selectedStudy))
    );

    return {
      name: matchingEffectSize?.intervention_tag_short_labels || 'Unknown Intervention',
      description: matchingEffectSize?.intervention_description || 'No intervention description available'
    };
  };

  const getOutcomeDetails = () => {
    const matchingEffectSize = flatEffectSizes.find(
      d => d.intervention_tag_ids.includes(interventionTagIds) &&
        d.outcome_tag_ids.includes(outcomeTagIds) &&
        (String(d.id) === String(selectedStudy) || String(d.paper_id) === String(selectedStudy))
    );

    return {
      name: matchingEffectSize?.outcome_tag_short_labels || 'Unknown Outcome',
      description: matchingEffectSize?.outcome_description || 'No outcome description available'
    };
  };

  useEffect(() => {
    if (activePlotDetails) {
      const rawPlotData = activePlotDetails?.plotDataInfo?.data;
      const resolvedPlotData = (rawPlotData && 'data' in rawPlotData && rawPlotData.data) ? rawPlotData.data : rawPlotData;
      setPlotData(resolvedPlotData);
      setFlatEffectSizes(resolvedPlotData?.flat_effect_sizes || []);

      if (activePlotDetails.citation_ids?.[0]?.value && activePlotDetails.citation_ids?.[1]?.value) {
        setInterventionTagIds(activePlotDetails.citation_ids[0].value);
        setOutcomeTagIds(activePlotDetails.citation_ids[1].value);
      } else if (resolvedPlotData?.flat_effect_sizes?.length > 0) {
        const firstPair = d3
          .groups(
            resolvedPlotData.flat_effect_sizes,
            (d) => `${d.outcome_tag_ids}_${d.intervention_tag_ids}`
          )
          .sort((a, b) => b[1].length - a[1].length)?.[0]?.[1]?.[0];

        if (firstPair) {
          setInterventionTagIds(firstPair.intervention_tag_ids);
          setOutcomeTagIds(firstPair.outcome_tag_ids);
        }
      }
    } else {
      setPlotData(null);
      setFlatEffectSizes([]);
      setInterventionTagIds('');
      setOutcomeTagIds('');
    }
  }, [activePlotDetails]);

  useEffect(() => {
    if (flatEffectSizes.length > 0 && onSelectStudy && !selectedStudy && interventionTagIds && outcomeTagIds) {
      const matchingEffectSizes = flatEffectSizes.filter(
        d => d.intervention_tag_ids.includes(interventionTagIds) &&
          d.outcome_tag_ids.includes(outcomeTagIds)
      );
      if (matchingEffectSizes.length > 0) {
        const firstStudyId = String(matchingEffectSizes[0].id || matchingEffectSizes[0].paper_id);
        onSelectStudy(firstStudyId);
      }
    }
  }, [flatEffectSizes, onSelectStudy, selectedStudy, interventionTagIds, outcomeTagIds]);

  if (
    !plotLoading &&
    (!activePlotDetails || !activePlotDetails.plotDataInfo?.data) ||
    plotError
  ) {
    return null;
  }

  const effectSizes2 =
    flatEffectSizes
      .filter(
        (d) =>
          d.intervention_tag_ids.includes(interventionTagIds) &&
          d.outcome_tag_ids.includes(outcomeTagIds)
      )
      ?.sort((a, b) => b.hedges_d - a.hedges_d) || [];

  const xDomain = [
    Math.min(
      0,
      d3.min(effectSizes2, (d) => d.standardized_ci_lower) ?? 0
    ),
    Math.max(
      0,
      d3.max(effectSizes2, (d) => d.standardized_ci_upper) ?? 0
    ),
  ];

  return (
    <Card
      elevation={0}
      sx={{
        maxWidth: "826px",
        width: "100%",
        height: "100%",
        maxHeight: "902px",
        border: `1px solid ${theme.palette.divider}`,
        borderRadius: "8px",
        display: "flex",
        flexDirection: "column",
        background: theme.common.white.main,
        p: 2,
      }}
    >
      <Box
        sx={{
          display: "flex",
          flexDirection: "column",
          flexGrow: 1,
          overflow: "hidden",
          gap: 2,
        }}
      >
        <Box
          id="comparison-view-header"
          sx={{
            display: "flex",
            justifyContent: "flex-end",
            alignItems: "center",
            p: 0,
            position: "sticky",
            top: 0,
            background: theme.common.white.main,
            zIndex: 1,
            flexShrink: 0,
          }}
        >
          <IconButton
            onClick={onClose}
            size="small"
            sx={{ ml: 0.5, p: 0.5, color: theme.palette.text.secondary }}
          >
            <CloseIcon fontSize="small" />
          </IconButton>
        </Box>

        <Box
          id="comparison-view-content"
          sx={{
            flexGrow: 1,
            display: "flex",
            flexDirection: "column",
            overflow: "hidden",
            gap: 2,
          }}
        >

          {/* Forest Plot Section */}
          <Box
            sx={{
              height: '43vh', // 440px/1024px
              minHeight: 0,
              display: 'flex',
              flexDirection: 'column',
              overflow: 'hidden',
              flexShrink: 0,
            }}
          >
            <Grid container spacing={0} sx={{ flexGrow: 1, height: '100%' }}>
              <Grid item xs={12} sx={{ height: '100%' }}>
                <Box
                  sx={{
                    display: 'flex',
                    flexDirection: 'column',
                    height: '100%',
                    border: `1px solid ${theme.palette.divider}`,
                    borderRadius: '4px',
                    p: 1,
                  }}
                >
                  <Typography
                    variant="body1"
                    sx={{
                      mb: 1,
                      pb: 0,
                      color: theme.palette.text.primary,
                      fontSize: '14px',
                      fontWeight: '500',
                    }}
                  >
                    <PairSelector
                      citationIds={citationIds}
                      messageId={messageIdFromPlot}
                      plotData={plotData}
                      onPairClicked={handleOnPairClicked}
                      currentInterventionId={interventionTagIds}
                      currentOutcomeId={outcomeTagIds}
                    />
                  </Typography>
                  {plotData &&
                    (Array.isArray(plotData)
                      ? plotData.length > 0
                      : Object.keys(plotData).length > 0) && (
                      <MeanEffectSizePlot
                        data={[outcomeTagIds, effectSizes2, interventionTagIds]}
                        xDomain={xDomain}
                      />
                    )}
                  {plotData &&
                    (Array.isArray(plotData)
                      ? plotData.length > 0
                      : Object.keys(plotData).length > 0) && (
                      <Box
                        id="data-visualization-tab-scroll"
                        sx={{
                          flexGrow: 1,
                          display: 'flex',
                          flexDirection: 'column',
                          p: 0,
                          overflow: 'auto',
                        }}
                      >
                        <Box
                          sx={{
                            flexGrow: 1,
                            display: 'flex',
                            flexDirection: 'column',
                          }}
                        >
                          <EffectSizesPlot2
                            data={[outcomeTagIds, effectSizes2, interventionTagIds]}
                            xDomain={xDomain}
                            onSelectStudy={onSelectStudy}
                            selectedStudy={selectedStudy}
                          />
                        </Box>
                      </Box>
                    )}
                </Box>
              </Grid>
            </Grid>
          </Box>

          {/* Source Card Section */}
          {(() => {
            if (
              !selectedStudy ||
              !Array.isArray(sources) ||
              sources.length === 0
            )
              return null;
            let selectedSource = sources.find(
              (s) => String(s.id) === String(selectedStudy)
            );
            if (!selectedSource) {
              const isNumeric = (val: any) =>
                !isNaN(val as number) && !isNaN(parseFloat(val as string));
              if (isNumeric(selectedStudy)) {
                selectedSource = sources.find(
                  (s) =>
                    isNumeric(s.paper_id) &&
                    Number(s.paper_id) === Number(selectedStudy)
                );
              }
            }
            if (!selectedSource) {
              return null;
            }
            return (
              <Box
                sx={{
                  height: '35vh',
                  border: `1px solid ${theme.palette.divider}`,
                  borderRadius: '8px',
                  backgroundColor: theme.common.white.main,
                  overflow: 'auto', // Only show scrollbars when content overflows
                  // Scrollbar styling - only visible when needed
                  scrollbarWidth: 'thin',
                  scrollbarColor: '#ABCCFC transparent',
                  '&::-webkit-scrollbar': {
                    width: '8px',
                    background: 'transparent',
                    borderRadius: '8px',
                  },
                  '&::-webkit-scrollbar-thumb': {
                    background: '#ABCCFC',
                    borderRadius: '8px',
                  },
                  '&::-webkit-scrollbar-thumb:hover': {
                    background: '#ABCCFC',
                  },
                  '&::-webkit-scrollbar-corner': {
                    background: 'transparent',
                  },
                  boxSizing: 'border-box',
                }}
              >
                  {(() => {
                    const interventionDetails = getInterventionDetails();
                    const outcomeDetails = getOutcomeDetails();

                    return (
                      <Sources
                        key={`sources-panel-selected-study`}
                        sources={[selectedSource]}
                        messageId={informationId}
                        selectedStudy={selectedStudy}
                        activeSourcePaperIds={activeSourcePaperIds}
                        activeSourceMessageId={activeSourceMessageId}
                        displayCloseButton={true}
                        onClose={handleCloseSourceCard}
                        expandable={false}
                        alwaysExpanded={true}
                        hideSourceCount={true}
                        hideArticleIcon={true}
                        maxAbstractLines={2}
                        disableSelectedBorder={true}
                        iconSpacing={2}
                        showCloseButton={false}
                        onCloseSource={handleCloseSourceCard}
                        interventionDetails={interventionDetails}
                        outcomeDetails={outcomeDetails}
                        hideBorder={true}
                      />
                    );
                  })()}
                </Box>
            );
          })()}
          {showSources && (
            <Box
              sx={{
                flex: isMobileOrTablet ? "0 0 184px" : "0 0 22%",
                overflowY: "auto",
                display: "flex",
                flexDirection: "column",
                borderRadius: "8px",
                p: 0,
              }}
            >
              <Sources
                key={`sources-panel-${informationId}`}
                onClose={() => {
                  setShowSources(false);
                }}
                sources={sources}
                messageId={informationId}
                selectedStudy=""
                activeSourcePaperIds={[]}
                activeSourceMessageId={null}
                displayCloseButton={true}
                maxAbstractLines={3}
                disableAutoScroll={true}
                disableSelectedBorder={true}
                disableSelectedHighlight={true}
              />
            </Box>
          )}
        </Box>
      </Box>
    </Card>
  );
};
export default memo(ComparisonView);