from typing import Optional
from dataclasses import dataclass
from dataclasses_json import dataclass_json


@dataclass_json
@dataclass
class DictRow:
    paper_id: str
    paper_combined_id: str
    title: Optional[str] = None
    year: Optional[int] = None
    doi_url: Optional[str] = None
    doi: Optional[str] = None
    authors: Optional[str] = None
    first_author: Optional[str] = None
    journal_name: Optional[str] = None
    country_code: Optional[str] = None
    country_name: Optional[str] = None
    region: Optional[str] = None
    income_group: Optional[str] = None
    quality_score: Optional[float] = None
    quality_score_category: Optional[str] = None
    treatment_arm: Optional[str] = None
    intervention_id: Optional[str] = None
    intervention_tag_ids: Optional[str] = None
    intervention_tag_labels: Optional[str] = None
    intervention_tag_short_labels: Optional[str] = None
    intervention_tag_definitions: Optional[str] = None
    intervention_target_populations: Optional[str] = None
    intervention_sectors: Optional[str] = None
    intervention_objective: Optional[str] = None
    intervention_scale: Optional[str] = None
    intervention_intensity: Optional[str] = None
    intervention_fidelity: Optional[str] = None
    intervention_description: Optional[str] = None
    intervention_analysis_unit: Optional[str] = None
    intervention_start_date: Optional[str] = None
    intervention_end_date: Optional[str] = None
    intervention_cost: Optional[str] = None
    attrition_lower: Optional[float] = None
    attrition_upper: Optional[float] = None
    compliance_rate_lower: Optional[float] = None
    compliance_rate_upper: Optional[float] = None
    intervention_labels: Optional[str] = None
    intervention_ids: Optional[str] = None
    outcome_ids: Optional[str] = None
    outcome_tag_ids: Optional[str] = None
    outcome_tag_labels: Optional[str] = None
    outcome_tag_short_labels: Optional[str] = None
    outcome_tag_definition: Optional[str] = None
    outcome_target_populations: Optional[str] = None
    outcome_sectors: Optional[str] = None
    outcome_description: Optional[str] = None
    outcome_analysis_unit: Optional[str] = None
    outcome_connotation: Optional[str] = None
    outcome_type: Optional[str] = None
    outcome_labels: Optional[str] = None
    is_primary_period: Optional[int] = None
    data_collection_round: Optional[str] = None
    cohen_d: Optional[float] = None
    hedges_d: Optional[float] = None
    standardized_ci_lower: Optional[float] = None
    standardized_ci_upper: Optional[float] = None
