[tool.poetry]
name = "impactai"
version = "0.1.0"
package-mode = false

[tool.black]
line-length = 88
target-version = ['py37', 'py38', 'py39', 'py310', 'py311']
include = '\.pyi?$'
extend-exclude = '\.ipynb$'

[tool.isort]
profile = "black"
multi_line_output = 3

[tool.ruff]
lint.select = ["F401"]  # Detect unused imports
fix = true              # Autofix by default
line-length = 88        # Match Black's default
target-version = "py311"

[tool.ruff.format]
# Use Black-compatible formatting
quote-style = "double"
indent-style = "space"
skip-magic-trailing-comma = false
line-ending = "auto"

[tool.pytest.ini_options]
pythonpath = ["agent", "backend"]
testpaths = ["backend", "agent"]
python_files = ["*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]

[tool.poetry.dependencies]
python = "^3.11"
pytest = "^8.1.1"
black = ">=24.2.0,<26.0.0"
ruff = ">=0.3.0,<0.10.0"
pre-commit = ">=3.6.2,<5.0.0"
pytest-cov = ">=4.1.0,<7.0.0"
ipykernel = "^6.29.5"
jupyter = "^1.0.0"
nbformat = "^5.9.2"
pytest-asyncio = ">=0.23.5,<0.26.0"
pytest-xdist="^3.6.1"
vertexai = "^1.71.1"
google-cloud-aiplatform = "^1.71.1"
honcho = "^2.0.0"
