"""Shared error handling for pipelines."""

import logging
from typing import Any, Dict

from src.pipelines.outputs import create_pipeline_response
from src.tools.entity_extractor import ExtractedEntities
from src.tools.sql_generator import QueryResult

logger = logging.getLogger(__name__)


class PipelineErrorHandler:
    """Shared error handling logic for pipelines."""

    def __init__(self, verbose: bool = False):
        self.verbose = verbose

    @staticmethod
    def has_meaningful_entities(entities: ExtractedEntities) -> bool:
        """Check if extracted entities contain meaningful information."""
        return entities and any(
            [
                entities.interventions,
                entities.outcomes,
                entities.intervention_sectors,
                entities.outcome_sectors,
            ]
        )

    @staticmethod
    def format_extended_entities(
        extended_entities: Dict[str, Any], verbose: bool = False
    ) -> str:
        """Format extended entities for user display."""
        # Group entities by type and extract clean labels
        all_concepts = {}
        sum_concepts = 0
        verbose_suggestions = "Explore related concepts"

        for key in [
            "extended_interventions",
            "extended_outcomes",
            "extended_intervention_sectors",
            "extended_outcome_sectors",
        ]:
            if extended_entities.get(key):
                sub_key = key.split("_")[-1]
                all_concepts[sub_key] = []
                verbose_suggestions += f"\n\t- {sub_key}:"
                for entity in extended_entities[key][:3]:
                    # Extract the clean label or mention, removing technical formatting
                    clean_label = entity.get("short_label", "")
                    tag_id = entity.get("id", "")[0]

                    if clean_label and clean_label not in all_concepts[sub_key]:
                        all_concepts[sub_key].append(f"{clean_label} <{tag_id}>")
                        sum_concepts += 1
                        verbose_suggestions += f"\n\t\t- [{clean_label}]({tag_id})"

            if verbose:
                logger.info(
                    f"Formatting extended entities for suggestions. Found {len(all_concepts[sub_key])} concepts: {all_concepts[sub_key]}"
                )

        # Create a natural, conversational suggestion
        if sum_concepts > 0:
            suggestion = verbose_suggestions
        else:
            suggestion = "explore related concepts instead"

        if verbose:
            logger.info(f"Generated suggestion: {suggestion}")

        return suggestion

    @staticmethod
    def handle_no_entities(
        intent: str, user_query: str, pipeline_name: str
    ) -> Dict[str, Any]:
        """Handle case when no meaningful entities are found."""
        return create_pipeline_response(
            intent=intent,
            status="stopped_no_entities",
            user_query=user_query,
            observation="Unable to identify specific research entities in the query.",
            pipeline_name=pipeline_name,
            thought="It looks like the entity linking did not match any meaningful entities for the current search terms. Let's ask politely the user to clarify the economic development topic they're interested in.",
            step_completed="entity_extraction",
        )

    @staticmethod
    def handle_no_data_found(
        intent: str, query_result: QueryResult, user_query: str, pipeline_name: str
    ) -> Dict[str, Any]:
        """Handle case when no data is found."""
        extended_entities = query_result.metadata.get("extended_entities", {})

        # Check if we have any extended entities to suggest
        has_suggestions = any(
            extended_entities.get(key, [])
            for key in [
                "extended_interventions",
                "extended_outcomes",
                "extended_intervention_sectors",
                "extended_outcome_sectors",
            ]
        )

        if has_suggestions:
            logger.info(
                f"No data found for query '{user_query}', but found related entities for suggestions"
            )
            suggestions = PipelineErrorHandler.format_extended_entities(
                extended_entities, verbose=True
            )
            logger.info(f"Generated suggestions for user: {suggestions}")

            observation = "The SQL tool did not match any data related to the user query, but entity linking found related concepts."
            status = "no_data_found_with_suggestions"
            thought = f"It looks like the DB doesn't have any data for the current search terms, but the system identified related concepts: {suggestions}. Let's suggest the user to rephrase the query in a natural way."
        else:
            logger.info(
                f"No data found for query '{user_query}' and no related entities found for suggestions"
            )
            observation = "The SQL tool did not match any data related to the user query, and entity linking did not find any related concepts."
            status = "no_data_found"
            thought = "It looks like the DB doesn't have any data for the current search terms, and entity linking did not find any related concepts. Let's suggest the user to rephrase the query in a natural way or ask a different question."

        return create_pipeline_response(
            intent=intent,
            status=status,
            user_query=user_query,
            observation=observation,
            pipeline_name=pipeline_name,
            thought=thought,
            step_completed="sql_generation",
            extended_entities=(
                extended_entities
                if status == "no_data_found_with_suggestions"
                else None
            ),
        )

    @staticmethod
    def handle_pipeline_error(
        intent: str, error: Exception, user_query: str, pipeline_name: str
    ) -> Dict[str, Any]:
        """Handle general pipeline errors."""
        logger.error(f"Pipeline error in {pipeline_name}: {error}")

        return create_pipeline_response(
            intent=intent,
            status="pipeline_error",
            user_query=user_query,
            observation="I encountered a technical issue while processing your query. Please try again.",
            pipeline_name=pipeline_name,
            thought="An unexpected error occurred during pipeline execution.",
            error=str(error),
        )
