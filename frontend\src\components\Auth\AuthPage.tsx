import { useState, useEffect } from "react";
import {
    <PERSON><PERSON><PERSON>,
    Card,
    CardContent,
    TextField,
    Button,
    Typography,
    Link,
    Box,
    Checkbox,
    FormControlLabel,
    useTheme,
    CircularProgress
} from "@mui/material";
import { useIsMobile, useIsTablet } from "../Layout/MobileUtils";
import Logo from "../Common/Logo/Logo";
import { registerUser, loginUser, forgotPassword, resetPassword } from "../../services/authService";
import { useNavigate, useLocation } from "react-router-dom";
import layer1 from './layer1.png';
import './auth.css';
import layer2 from './layer2.png';

const AuthPage = () => {
    const theme = useTheme();
    const [mode, setMode] = useState<"login" | "signup" | "forgotPassword" | "resetPassword">("login");
    const [name, setName] = useState("");
    const [email, setEmail] = useState("");
    const [password, setPassword] = useState("");
    const [termsAgreed, setTermsAgreed] = useState(false);
    const [confirmPassword, setConfirmPassword] = useState("");
    const [error, setError] = useState("");
    const navigate = useNavigate();
    const isMobile = useIsMobile();
    const isTablet = useIsTablet();
    const location = useLocation();
    const searchParams = new URLSearchParams(location.search);
    const resetToken = searchParams.get("token");
    const [isSubmitting, setIsSubmitting] = useState(false);

    const textFieldStyle = {
        marginBottom: theme.spacing(2),
        "& .MuiOutlinedInput-root": {
            "& fieldset": {
                borderColor: theme.palette.primary.outlinedBorder,
                borderWidth: '1px'
            },
            ".MuiOutlinedInput-notchedOutline": {
                borderWidth: '1px',
            },
            "&:hover fieldset": {
                borderColor: theme.palette.secondary.main,
                borderWidth: '1px'
            },
            "&.Mui-focused fieldset": {
                borderColor: theme.palette.secondary.main,
                borderWidth: '1px'
            },
        },
    };

    const nameTextFieldStyle = {
        marginBottom: theme.spacing(2),
        "& .MuiInput-underline": {
            '&::before': {
                borderBottom: `1px solid rgba(131, 179, 252, 0.50)`,
                borderWidth: '1px',
            },
            '&:hover:not(.Mui-disabled, .Mui-error)::before': {
                borderBottom: `1px solid ${theme.palette.secondary.main} !important`,
                borderWidth: '1px',
            },
            '&.Mui-focused::before': {
                borderBottom: `1px solid ${theme.palette.primary.outlinedBorder} !important`,
                borderWidth: '1px',
            },
            '&.Mui-focused': {
                '&::after': {
                    borderBottom: `1px solid ${theme.palette.primary.outlinedBorder} !important`,
                    borderWidth: '1px !important',
                },
            },
        },
        "& .MuiInput-input": {
            padding: '8px 0px',
        },
        "& .MuiInputLabel-root": {
            fontSize: "16px !important",
            color: "rgba(0, 51, 128, 0.70"
        }
    };
    const buttonStyle = {
        mb: 2,
        display: "flex",
        padding: "8px 22px",
        justifyContent: "center",
        alignItems: "center",
        alignSelf: "stretch",
        borderRadius: 40,
        fontFamily: 'Roboto',
        fontSize: '15px',
        fontWeight: 600,
        boxShadow: "0px 1px 5px 0px rgba(0, 0, 0, 0.05), 0px 2px 2px 0px rgba(0, 0, 0, 0.05), 0px 3px 1px -2px rgba(0, 0, 0, 0.05)",
        "&:hover": {
            backgroundColor: theme.palette.primary.dark,
        },
        "&:disabled": {
            backgroundColor: "#D4E4FC",
            color: theme.palette.text.disabled,
            boxShadow: "none",
            cursor: 'default',
        },

    };

    const linkBoxStyle = {
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        justifyContent: "center",
        gap: theme.spacing(1),
        width: "100%",
    };

    const linkStyle = {
        color: theme.palette.primary.main,
        cursor: "pointer",
        textDecoration: "underline",
        "&:hover": { textDecoration: "underline" },
    };

    useEffect(() => {
        const token = localStorage.getItem("token");
        if (token) {
            navigate("/");
        }
    }, [navigate]);

    useEffect(() => {
        if (resetToken) {
            setMode("resetPassword");
        }
    }, [resetToken]);

    const resetFormFields = () => {
        setName("");
        setEmail("");
        setPassword("");
        setTermsAgreed(false);
        setConfirmPassword("");
        setError("");
    };

    const handleModeChange = (newMode: "login" | "signup" | "forgotPassword" | "resetPassword") => {
        setMode(newMode);
        resetFormFields();
    };

    const handleTermsChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        setTermsAgreed(event.target.checked);
    };

    const handleForgotPasswordSubmit = async () => {
        setIsSubmitting(true);
        try {
            setError("");
            await forgotPassword(email);
            alert("Password reset link sent to your email.");
            setMode("login");
        } catch (err: any) {
            setError(err?.message || "An error occurred.");
        } finally {
            setIsSubmitting(false);
        }
    };

    const handleResetPasswordSubmit = async () => {
        setIsSubmitting(true);
        try {
            setError("");
            if (password !== confirmPassword) {
                setError("Passwords do not match.");
                return;
            }
            if (!resetToken) {
                setError("Invalid reset token.");
                return;
            }
            await resetPassword(resetToken, password);
            alert("Password has been reset successfully.");
            navigate("/login");
        } catch (err: any) {
            setError(err?.message || "An error occurred.");
        } finally {
            setIsSubmitting(false);
        }
    };

    const handleAuth = async (authType: "login" | "signup") => {
        setIsSubmitting(true);
        try {
            setError("");
            let userData;
            if (authType === "signup") {
                if (!termsAgreed) {
                    setError("Please agree to the Terms & Conditions and Privacy Policy.");
                    return;
                }
                userData = await registerUser(email, password, name);
            } else if (authType === "login") {
                userData = await loginUser(email, password);
            }
            if (userData?.token) {
                localStorage.setItem("userData", JSON.stringify(userData));
                localStorage.setItem("token", userData.token);
                navigate("/");
            }
        } catch (err: any) {
            setError(err?.message || "An error occurred.");
        } finally {
            setIsSubmitting(false);
        }
    };

    const renderLeftColumn = (image: string) => (
        <Box
            sx={{
                display: "flex",
                flexDirection: "column",
                justifyContent: "center",
                alignItems: "center",
                height: "100%",
                width: "100%",
                backgroundImage: `url("${image}")`,
                backgroundSize: "cover",
                backgroundPosition: "center",
                backgroundRepeat: 'no-repeat'
            }}
        >
            <Logo width={200} height={46} logoSrc="" altText="WorldBank Impact AI" color="white" />
        </Box>
    );

    const authCardStyle = {
        display: "flex",
        flexDirection: "column",
        padding: (theme) => theme.spacing(3),
        alignItems: "flex-start",
        width: (mode === "login" || mode === "signup" || mode === "forgotPassword" || mode === "resetPassword")
            ? isMobile
                ? 'auto'
                : isTablet
                    ? '80%'
                    : '400px'
            : 'auto',
        bgcolor: theme.palette.common.white,
        borderRadius: '24px',
    };

    const authCardContentStyle = {
        display: "flex",
        flexDirection: "column",
        alignItems: "flex-start",
        width: "100%",
        p: 0,
        "&:last-child": { paddingBottom: 0 },
    };

    const headerStyle = {
        color: theme.palette.text.primary,
        fontSize: isMobile ? '24px' : '30px',
        textAlign: 'center',
        fontFamily: 'HostGrotesk',
        lineHeight: '133.4%',
        fontStyle: 'normal',
        fontWeight: '400',
        width: '100%'
    };

    const subHeaderStyle = {
        color: theme.palette.text.primary,
        fontSize: '12px',
        textAlign: 'center',
        fontFamily: 'Roboto',
        lineHeight: '166%',
        fontStyle: 'normal',
        fontWeight: '400'
    };

    const getSubmitButtonSx = (theme, isSubmitting) => ({
        ...buttonStyle,
        ...(isSubmitting && {
            "&:disabled": {
                backgroundColor: theme.palette.primary.main,
                color: theme.palette.common.white,
                cursor: 'default',
                pointerEvents: 'none',
            },
        }),
    });

    const renderSubmitButtonContent = (label, isSubmitting) => (
        <Box sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            width: '100%',
        }}>
            {isSubmitting ? (
                <>
                    {label}
                    &nbsp;
                    <CircularProgress size="24px" color="inherit" />
                </>
            ) : (
                label
            )}
        </Box>
    );

    const renderLoginForm = () => (
        <Card
            elevation={0}
            sx={authCardStyle}
        >
            <CardContent sx={authCardContentStyle}>
                <TextField
                    label="Email"
                    variant="outlined"
                    fullWidth
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    sx={textFieldStyle}
                    onKeyDown={(e) => e.key === "Enter" && handleAuth("login")}
                />
                <TextField
                    label="Password"
                    variant="outlined"
                    type="password"
                    fullWidth
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    sx={textFieldStyle}
                    onKeyDown={(e) => e.key === "Enter" && handleAuth("login")}
                />
                <Button
                    variant="contained"
                    fullWidth
                    size="large"
                    sx={getSubmitButtonSx(theme, isSubmitting)}
                    onClick={() => handleAuth("login")}
                    disabled={!email || !password || isSubmitting}
                >
                    {renderSubmitButtonContent("Log In", isSubmitting)}
                </Button>
                <Box sx={linkBoxStyle}>
                    <Box sx={{ display: 'flex', flexDirection: 'row', alignItems: 'center' }}>
                        <Typography variant="body2" component="span">
                            Don't have an account?
                        </Typography>
                        &nbsp;
                        <Link
                            component="button"
                            variant="body2"
                            onClick={() => handleModeChange("signup")}
                            sx={{ ...linkStyle, display: 'inline', ...(isSubmitting && { color: theme.palette.primary.main, pointerEvents: 'none' }) }}
                        >
                            Sign Up
                        </Link>
                    </Box>
                    <Link
                        component="button"
                        variant="body2"
                        onClick={() => handleModeChange("forgotPassword")}
                        sx={linkStyle}
                    >
                        Forgot Password?
                    </Link>
                </Box>
            </CardContent>
        </Card>
    );

    const renderSignupForm = () => (
        <Card
            elevation={0}
            sx={authCardStyle}
        >
            <CardContent sx={authCardContentStyle}>
                <TextField
                    label="Name"
                    placeholder="Your Name"
                    variant="standard"
                    fullWidth
                    InputLabelProps={{
                        shrink: true,
                        style: {
                            color: theme.palette.text.secondary,
                        },
                    }}
                    sx={nameTextFieldStyle}
                    value={name}
                    onChange={(e) => setName(e.target.value)}
                    onKeyDown={(e) => e.key === "Enter" && handleAuth("signup")}
                />
                <TextField
                    label="Email"
                    variant="outlined"
                    fullWidth
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    sx={textFieldStyle}
                    onKeyDown={(e) => e.key === "Enter" && handleAuth("signup")}
                />
                <TextField
                    label="Password"
                    variant="outlined"
                    type="password"
                    fullWidth
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    sx={textFieldStyle}
                    onKeyDown={(e) => e.key === "Enter" && handleAuth("signup")}
                />
                <Box sx={{ width: '100%', mt: 0, mb: 2 }}>
                    <FormControlLabel
                        control={
                            <Checkbox
                                id="terms"
                                checked={termsAgreed}
                                onChange={handleTermsChange}
                                sx={{
                                    color: theme.palette.action.active,
                                    '&.Mui-checked': {
                                        color: theme.palette.action.active,
                                    },
                                }}
                            />}
                        label={
                            <Typography variant="body2">
                                I agree to the{' '}
                                <Link component="button" sx={linkStyle} onClick={() => { /* Terms link handler */ }}>
                                    Terms &amp; Conditions
                                </Link>{' '}
                                and{' '}
                                <Link component="button" sx={linkStyle} onClick={() => { /* Terms link handler */ }}>
                                    Privacy Policy
                                </Link>
                            </Typography>
                        }
                        sx={{
                            display: 'flex',
                            alignItems: 'center',
                            gap: '0px',
                            '& .MuiFormControlLabel-label': {
                                display: 'flex',
                                alignItems: 'center',
                                gap: '4px',
                            },
                        }}
                    />
                </Box>
                <Button
                    variant="contained"
                    color="primary"
                    fullWidth
                    size="large"
                    sx={getSubmitButtonSx(theme, isSubmitting)}
                    onClick={() => handleAuth("signup")}
                    disabled={!email || !password || !termsAgreed || isSubmitting}
                >
                    {renderSubmitButtonContent("Sign Up", isSubmitting)}
                </Button>
                <Box sx={linkBoxStyle}>
                    <Box sx={{ display: 'flex', flexDirection: 'row', alignItems: 'center' }}>
                        <Typography variant="body2" component="span">
                            Have an account?
                        </Typography>
                        &nbsp;
                        <Link
                            component="button"
                            variant="body2"
                            onClick={() => handleModeChange("login")}
                            sx={{ ...linkStyle, display: 'inline' }}
                        >
                            Log In
                        </Link>
                    </Box>
                </Box>
            </CardContent>
        </Card>
    );

    const renderForgotPasswordForm = () => (
        <Card
            elevation={0}
            sx={{ ...authCardStyle }}
        >
            <CardContent sx={authCardContentStyle}>
                <TextField
                    label="Email"
                    variant="outlined"
                    fullWidth
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    sx={textFieldStyle}
                    onKeyDown={(e) => e.key === "Enter" && handleForgotPasswordSubmit()}
                />
                <Button
                    variant="contained"
                    color="primary"
                    fullWidth
                    size="large"
                    sx={getSubmitButtonSx(theme, isSubmitting)}
                    onClick={handleForgotPasswordSubmit}
                    disabled={!email || isSubmitting}
                >
                    {renderSubmitButtonContent("Send Reset Link", isSubmitting)}
                </Button>
                <Box sx={linkBoxStyle}>
                    <Link
                        component="button"
                        variant="body2"
                        onClick={() => handleModeChange("login")}
                        sx={linkStyle}
                    >
                        Back to Log In
                    </Link>
                </Box>
            </CardContent>
        </Card>
    );

    const renderResetPasswordForm = () => (
        <Card
            elevation={0}
            sx={{ ...authCardStyle }}
        >
            <CardContent sx={authCardContentStyle}>
                <TextField
                    label="New Password"
                    variant="outlined"
                    type="password"
                    fullWidth
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    sx={textFieldStyle}
                    onKeyDown={(e) => e.key === "Enter" && handleResetPasswordSubmit()}
                />
                <TextField
                    label="Confirm New Password"
                    variant="outlined"
                    type="password"
                    fullWidth
                    value={confirmPassword}
                    onChange={(e) => setConfirmPassword(e.target.value)}
                    sx={textFieldStyle}
                    onKeyDown={(e) => e.key === "Enter" && handleResetPasswordSubmit()}
                />
                {error && (
                    <Typography color="error" sx={{ mt: 1 }}>
                        {error}
                    </Typography>
                )}
                <Button
                    variant="contained"
                    color="primary"
                    fullWidth
                    size="large"
                    sx={getSubmitButtonSx(theme, isSubmitting)}
                    onClick={handleResetPasswordSubmit}
                    disabled={!password || !confirmPassword || isSubmitting}
                >
                    {renderSubmitButtonContent("Save New Password", isSubmitting)}
                </Button>
                <Box sx={linkBoxStyle}>
                    <Link component="button" variant="body2" onClick={() => navigate("/login")} sx={linkStyle}>
                        Back to Log In
                    </Link>
                </Box>
            </CardContent>
        </Card>
    );
    const renderAuthLayout = () => (
        <Box
            className='auth-desktop-layout'
            sx={{
                width: "100%",
                height: "100%",
                display: "flex",
                flexDirection: (mode === "login" || mode === "signup") ? "row" : "column",
            }}
        >
            {/* Left Column - Image (only for signup and login) */}
            {(mode === "signup" || mode === "login") && (
                <Box
                    sx={{
                        flex: '0 0 40%',
                        display: 'flex',
                        justifyContent: 'center',
                        alignItems: 'center',
                    }}
                >
                    <Box
                        sx={{
                            width: '100%',
                            height: '100%',
                        }}
                    >
                        {renderLeftColumn(mode === "signup" ? layer1 : layer2)}
                    </Box>
                </Box>
            )}

            {/* Right Column - Form */}
            <Box
                sx={{
                    flex: (mode === "login" || mode === "signup") ? '0 0 60%' : '1 1 auto',
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                    justifyContent: 'center',
                    width: '100%',
                    height: '100%',
                }}
            >
                <Box
                    sx={{
                        display: 'flex',
                        flexDirection: 'column',
                        alignItems: 'center',
                        width: '90%',
                        marginBottom: 2,
                        gap: '24px',
                    }}
                >
                    {mode === "login" && (
                        <Box
                            sx={{
                                display: 'flex',
                                flexDirection: 'column',
                                alignItems: 'center',
                                gap: '8px'
                            }}
                        >
                            <Typography variant="h5" sx={headerStyle}>
                                Welcome Back
                            </Typography>
                            <Typography variant="caption" sx={subHeaderStyle}>
                                Please enter your details below
                            </Typography>
                        </Box>
                    )}
                    {mode === "signup" && (
                        <Box
                            sx={{
                                display: 'flex',
                                flexDirection: 'column',
                                alignItems: 'center',
                                gap: '8px'
                            }}
                        >
                            <Typography variant="h5" sx={headerStyle} component="div" textAlign="center">
                                <div>Easier access, greater impact</div>
                                <div>Research evidence to action, in seconds</div>
                            </Typography>
                        </Box>
                    )}
                    {mode === "forgotPassword" && (
                        <Box
                            sx={{
                                display: 'flex',
                                flexDirection: 'column',
                                alignItems: 'center',
                                gap: '8px'
                            }}
                        >
                            <Typography variant="h5" sx={headerStyle}>
                                Forgot Password?
                            </Typography>
                            <Typography variant="caption" sx={subHeaderStyle}>
                                No worries, we’ll email you reset instructions
                            </Typography>
                        </Box>
                    )}
                    {mode === "resetPassword" && (
                        <Box
                            sx={{
                                display: 'flex',
                                flexDirection: 'column',
                                alignItems: 'center',
                                gap: '8px'
                            }}
                        >
                            <Typography variant="h5" sx={headerStyle}>
                                Reset Password?
                            </Typography>
                            <Typography variant="caption" sx={subHeaderStyle}>
                                Secure your account with a new password
                            </Typography>
                        </Box>
                    )}
                </Box>
                {mode === "login" && renderLoginForm()}
                {mode === "signup" && renderSignupForm()}
                {mode === "forgotPassword" && renderForgotPasswordForm()}
                {mode === "resetPassword" && renderResetPasswordForm()}
                {error && mode !== "resetPassword" && (
                    <Typography variant="body1" color="error" sx={{ mt: 1, textAlign: 'center' }}>
                        {error}
                    </Typography>
                )}
            </Box>
        </Box>
    );

    const renderMobileLayout = () => (
        <Box
            className="left-column-mobile"
            sx={{
                width: "100%",
                height: "100%",
                display: "flex",
                flexDirection: "column",
                alignItems: "center",
                justifyContent: "stretch",
                gap: '24px',
                padding: 0,
            }}
        >
            {((mode === "signup") || (mode === "login")) && (
                <Box sx={{ width: '100%' }}>
                    {renderLeftColumn(mode === "signup" ? layer1 : layer2)}
                </Box>
            )}
            <Box sx={{
                width: '90%',
                flex: 1,
                display: 'flex',
                flexDirection: 'column',
                justifyContent: 'center',
                alignItems: 'stretch'
            }}>
                <Box
                    sx={{
                        display: 'flex',
                        flexDirection: 'column',
                        alignItems: 'center',
                        marginBottom: 2,
                        gap: '24px',
                    }}
                >
                    {mode === "login" && (
                        <Box
                            sx={{
                                display: 'flex',
                                flexDirection: 'column',
                                alignItems: 'center',
                                gap: '8px',
                            }}
                        >
                            <Typography variant="h5" sx={headerStyle}>
                                Welcome Back
                            </Typography>
                            <Typography variant="caption" sx={subHeaderStyle}>
                                Please enter your details below
                            </Typography>
                        </Box>
                    )}
                    {mode === "signup" && (
                        <Box
                            sx={{
                                display: 'flex',
                                flexDirection: 'column',
                                alignItems: 'center',
                                gap: '8px'
                            }}
                        >
                            <Typography variant="h5" sx={headerStyle} component="div" textAlign="center">
                                <div>Easier access, greater impact</div>
                                <div>Research evidence to action, in seconds</div>
                            </Typography>
                        </Box>
                    )}
                    {mode === "forgotPassword" && (
                        <Box
                            sx={{
                                display: 'flex',
                                flexDirection: 'column',
                                alignItems: 'center',
                                gap: '8px'
                            }}
                        >
                            <Typography variant="h5" sx={headerStyle}>
                                Forgot Password?
                            </Typography>
                            <Typography variant="caption" sx={subHeaderStyle}>
                                No worries, we’ll email you reset instructions
                            </Typography>
                        </Box>
                    )}
                    {mode === "resetPassword" && (
                        <Box
                            sx={{
                                display: 'flex',
                                flexDirection: 'column',
                                alignItems: 'center',
                                gap: '8px'
                            }}
                        >
                            <Typography variant="h5" sx={headerStyle}>
                                Reset Password?
                            </Typography>
                            <Typography variant="caption" sx={subHeaderStyle}>
                                Secure your account with a new password
                            </Typography>
                        </Box>
                    )}
                </Box>
                {mode === "login" && renderLoginForm()}
                {mode === "signup" && renderSignupForm()}
                {mode === "forgotPassword" && renderForgotPasswordForm()}
                {mode === "resetPassword" && renderResetPasswordForm()}
                {error && mode !== "resetPassword" && (
                    <Typography variant="body1" color="error" sx={{ mt: 1, textAlign: 'center' }}>
                        {error}
                    </Typography>
                )}
            </Box>
        </Box>
    );

    const renderTabletLayout = () => (
        <Box
            sx={{
                width: "100%",
                height: "100%",
                display: "flex",
                alignItems: "stretch",
                justifyContent: "center",
                flexDirection: (mode === "login" || mode === "signup") ? "row" : "column",
                gap: 0,
            }}
        >
            {/* Left Column - Image */}
            {(mode === "signup" || mode === "login") && (
                <Box
                    sx={{
                        flex: '0 0 40%',
                        display: 'flex',
                        justifyContent: 'stretch',
                        alignItems: 'stretch',
                    }}
                >
                    {((mode === "signup") || (mode === "login")) && (
                        <Box sx={{ width: '100%', height: 'auto' }}>
                            {renderLeftColumn(mode === "signup" ? layer1 : layer2)}
                        </Box>
                    )}
                </Box>
            )}

            {/* Right Column - Form */}
            <Box
                sx={{
                    flex: '0 0 60%',
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                    justifyContent: 'center',
                    width: '100%',
                    height: '80%',
                    py: 2,
                }}
            >
                <Box
                    sx={{
                        display: 'flex',
                        flexDirection: 'column',
                        alignItems: 'center',
                        width: '90%',
                        marginBottom: 2,
                        gap: '24px',
                    }}
                >
                    {mode === "login" && (
                        <Box
                            sx={{
                                display: 'flex',
                                flexDirection: 'column',
                                alignItems: 'center',
                                gap: '8px'
                            }}
                        >
                            <Typography variant="h5" sx={headerStyle}>
                                Welcome Back
                            </Typography>
                            <Typography variant="caption" sx={subHeaderStyle}>
                                Please enter your details below
                            </Typography>
                        </Box>
                    )}
                    {mode === "signup" && (
                        <Box
                            sx={{
                                display: 'flex',
                                flexDirection: 'column',
                                alignItems: 'center',
                                gap: '8px'
                            }}
                        >
                            <Typography variant="h5" sx={headerStyle} component="div" textAlign="center">
                                <div>Easier access, greater impact</div>
                                <div>Research evidence to action, in seconds</div>
                            </Typography>
                        </Box>
                    )}
                    {mode === "forgotPassword" && (
                        <Box
                            sx={{
                                display: 'flex',
                                flexDirection: 'column',
                                alignItems: 'center',
                                gap: '8px'
                            }}
                        >
                            <Typography variant="h5" sx={headerStyle}>
                                Forgot Password?
                            </Typography>
                            <Typography variant="caption" sx={subHeaderStyle}>
                                No worries, we’ll email you reset instructions
                            </Typography>
                        </Box>
                    )}
                    {mode === "resetPassword" && (
                        <Box
                            sx={{
                                display: 'flex',
                                flexDirection: 'column',
                                alignItems: 'center',
                                gap: '8px'
                            }}
                        >
                            <Typography variant="h5" sx={headerStyle}>
                                Reset Password?
                            </Typography>
                            <Typography variant="caption" sx={subHeaderStyle}>
                                Secure your account with a new password
                            </Typography>
                        </Box>
                    )}
                </Box>
                {mode === "login" && renderLoginForm()}
                {mode === "signup" && renderSignupForm()}
                {mode === "forgotPassword" && renderForgotPasswordForm()}
                {mode === "resetPassword" && renderResetPasswordForm()}
                {error && mode !== "resetPassword" && (
                    <Typography variant="body1" color="error" sx={{ mt: 1, textAlign: 'center' }}>
                        {error}
                    </Typography>
                )}
            </Box>
        </Box>
    );

    return (
        <Container
            sx={{
                display: "flex",
                flexDirection: "column",
                alignItems: "stretch",
                justifyContent: "center",
                height: "100vh",
                p: '0px !important',
                maxWidth: "100% !important",
                background: theme.elevation.paperElevationZero,
            }}
        >
            {isMobile ? renderMobileLayout() : isTablet ? renderTabletLayout() : renderAuthLayout()}
        </Container>
    );
};

export default AuthPage;