import React from 'react';
import Markdown from 'markdown-to-jsx';
import LinkComponent from "./LinkComponent";
import { replaceTags } from "./Utils";

interface StaticMarkdownProps {
    text: string;
    sources?: any;
    plotData?: any;
    onViewOnPlotClicked: (payload: { citation_ids: { key: string; value: string }[]; messageId: string; plotDataInfo?: any }) => void;
    onViewOnSourceClicked: (payload: { paper_ids: string[]; messageId: string }) => void;
    messageId?: string;
}

const StaticMarkdown = ({
    text,
    sources,
    plotData,
    onViewOnPlotClicked,
    onViewOnSourceClicked,
    messageId
}: StaticMarkdownProps) => {

    const markdownOptions = {
        overrides: {
            ol: {
                component: (props: any) => (
                    <ol start={props.start}>
                        {React.Children.map(props.children, (child: any, index: number) => (
                            <li key={index}>
                                {React.Children.map(child.props.children, (grandChild: any) => {
                                    if (React.isValidElement(grandChild) && grandChild.type === 'a') {
                                        return (
                                            <LinkComponent
                                                {...grandChild.props}
                                                messageId={messageId || ''}
                                                onViewOnPlotClicked={onViewOnPlotClicked}
                                                onViewOnSourceClicked={onViewOnSourceClicked}
                                                plotData={plotData}
                                            />
                                        );
                                    }
                                    return grandChild;
                                })}
                            </li>
                        ))}
                    </ol>
                ),
            },
            li: {
                component: (props: any) => (
                    <li>
                        {React.Children.map(props.children, (child: any) => {
                            if (React.isValidElement(child) && child.type === 'a') {
                                return (
                                    <LinkComponent
                                        {...child.props}
                                        messageId={messageId || ''}
                                        onViewOnPlotClicked={onViewOnPlotClicked}
                                        onViewOnSourceClicked={onViewOnSourceClicked}
                                        plotData={plotData}
                                    />
                                );
                            }
                            return child;
                        })}
                    </li>
                ),
            },
            h3: {
                component: (props: any) => (<h3 {...props} />)
            },
            p: {
                component: (props: any) => (
                    <div>
                        {React.Children.map(props.children, (child: any) => {
                            if (React.isValidElement(child) && child.type === 'a') {
                                return (
                                    <LinkComponent
                                        {...child.props}
                                        messageId={messageId || ''}
                                        onViewOnPlotClicked={onViewOnPlotClicked}
                                        onViewOnSourceClicked={onViewOnSourceClicked}
                                        plotData={plotData}
                                    />
                                );
                            }
                            return child;
                        })}
                    </div>
                ),
            },
            em: {
                component: ({ children }: { children: React.ReactNode }) => <em>{children}</em>
            },
        },
    };

    return (
        <Markdown
            className="markdown-container"
            options={markdownOptions}
        >
            {replaceTags(text, sources, plotData)}
        </Markdown>
    );
};

export default StaticMarkdown;