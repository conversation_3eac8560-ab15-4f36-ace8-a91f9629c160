import { useState, useEffect } from "react";
import {
  Box,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
} from "@mui/material";
import { useTheme } from "@mui/material/styles";
import "./FunnelPlot.css";
import * as d3 from "d3";

interface PairSelectorProps {
  citationIds: { key: string; value: string }[];
  messageId: string;
  plotData: any;
  onPairClicked: (pair: { intervention: string; outcome: string }) => void;
  currentInterventionId: string | null;
  currentOutcomeId: string | null;
}

const PairSelector = ({
  citationIds,
  messageId,
  plotData,
  onPairClicked,
  currentInterventionId,
  currentOutcomeId,
}: PairSelectorProps) => {
  const theme = useTheme();

  const [selectedIntervention, setSelectedIntervention] = useState<string | null>(
    currentInterventionId
  );
  const [selectedOutcome, setSelectedOutcome] = useState<string | null>(
    currentOutcomeId
  );

  const flatEffectSizes = plotData?.flat_effect_sizes || [];

  const outcomePairs = d3
    .groups(
      flatEffectSizes.filter(
        (d: any) =>
          String(d.intervention_tag_ids) === String(selectedIntervention)
      ),
      (d: any) => `${d.intervention_tag_ids}_${d.outcome_tag_ids}`
    )
    .sort((a, b) => b[1].length - a[1].length)
    .map(([pair, studies]) => ({
      interventionLabel: studies[0].intervention_tag_short_labels,
      outcomeLabel: studies[0].outcome_tag_short_labels,
      intervention_id: studies[0].intervention_tag_ids,
      outcome_id: studies[0].outcome_tag_ids,
      pair: pair,
      studies: studies,
      uniqueStudiesCount: Array.from(new Set(studies.map((s: any) => s.study_id)))
        .length,
      meanEffectSize: d3.mean(studies, (d: any) => d.hedges_d),
    }))
    .slice(0, 10);

  const interventionPairs = d3
    .groups(
      flatEffectSizes.filter(
        (d: any) => String(d.outcome_tag_ids) === String(selectedOutcome)
      ),
      (d: any) => `${d.intervention_tag_ids}_${d.outcome_tag_ids}`
    )
    .sort((a, b) => b[1].length - a[1].length)
    .map(([pair, studies]) => ({
      interventionLabel: studies[0].intervention_tag_short_labels,
      outcomeLabel: studies[0].outcome_tag_short_labels,
      intervention_id: studies[0].intervention_tag_ids,
      outcome_id: studies[0].outcome_tag_ids,
      pair: pair,
      studies: studies,
      uniqueStudiesCount: Array.from(new Set(studies.map((s: any) => s.study_id)))
        .length,
      meanEffectSize: d3.mean(studies, (d: any) => d.hedges_d),
    }))
    .slice(0, 10);

  const maxUpperLower = d3.max(flatEffectSizes, (d: any) =>
    Math.max(Math.abs(d.hedges_d), Math.abs(d.hedges_d))
  );

  const cScale = d3
    .scaleQuantize()
    .domain([-maxUpperLower, maxUpperLower])
    .range([
      "#67001f",
      "#b2182b",
      "#d6604d",
      "#f4a582",
      "#fddbc7",
      "#f7f7f7",
      "#d1e5f0",
      "#92c5de",
      "#4393c3",
      "#2166ac",
      "#053061",
    ]);

  useEffect(() => {
    setSelectedIntervention(currentInterventionId);
    setSelectedOutcome(currentOutcomeId);
  }, [currentInterventionId, currentOutcomeId, plotData]);

  return (
    <div
      style={{
        width: "100%",
        display: "flex",
        flexDirection: "column",
        gap: "2px",
      }}
    >
      <div
        style={{
          display: "grid",
          gridTemplateColumns: "1fr 1fr",
          gap: "8px",
          cursor: "pointer",
        }}
      >
        <Box>
          <FormControl fullWidth>
            <InputLabel id="intervention-select-label">Intervention</InputLabel>
            <Select
              labelId="intervention-select-label"
              id="intervention-select"
              value={selectedIntervention}
              label="Intervention"
              onChange={(e) => {
                const interventionId = String(e.target.value);

                onPairClicked({
                  intervention: interventionId,
                  outcome: String(selectedOutcome),
                });

                setSelectedIntervention(interventionId);
              }}
            >
              {interventionPairs
                .sort((a, b) => (b.meanEffectSize || 0) - (a.meanEffectSize || 0))
                .map((pair) => (
                  <MenuItem key={pair.intervention_id} value={pair.intervention_id}>
                    <div>
                      <span
                        style={{
                          display: "inline-block",
                          width: "12px",
                          height: "12px",
                          backgroundColor: cScale(pair.meanEffectSize || 0),
                          borderRadius: "12px",
                          border: "1px solid rgba(0, 51, 128, 0.7)",
                          marginRight: "4px",
                          transform: "translateY(2px)",
                        }}
                      ></span>
                      {pair.interventionLabel}
                    </div>
                  </MenuItem>
                ))}
            </Select>
          </FormControl>
        </Box>
        <Box>
          <FormControl fullWidth>
            <InputLabel id="outcome-select-label">Outcome</InputLabel>
            <Select
              labelId="outcome-select-label"
              id="outcome-select"
              value={selectedOutcome}
              label="Outcome"
              onChange={(e) => {
                const outcomeId = String(e.target.value);

                onPairClicked({
                  intervention: String(selectedIntervention),
                  outcome: outcomeId,
                });

                setSelectedOutcome(outcomeId);
              }}
            >
              {outcomePairs
                .sort((a, b) => (b.meanEffectSize || 0) - (a.meanEffectSize || 0))
                .map((pair) => (
                  <MenuItem key={pair.outcome_id} value={pair.outcome_id}>
                    <div>
                      <span
                        style={{
                          display: "inline-block",
                          width: "12px",
                          height: "12px",
                          backgroundColor: cScale(pair.meanEffectSize || 0),
                          borderRadius: "12px",
                          border: "1px solid rgba(0, 51, 128, 0.7)",
                          marginRight: "4px",
                          transform: "translateY(2px)",
                        }}
                      ></span>
                      {pair.outcomeLabel}
                    </div>
                  </MenuItem>
                ))}
            </Select>
          </FormControl>
        </Box>
      </div>
    </div>
  );
};

export default PairSelector;