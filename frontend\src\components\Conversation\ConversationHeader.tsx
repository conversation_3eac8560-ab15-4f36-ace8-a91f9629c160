import { Typography, Avatar, Box } from '@mui/material';
import { useTheme } from '@mui/material/styles';
import {
    PRESENTATION_HEADLINE,
} from "../../utils/labels";

const ConversationHeader = ({ isMobile }) => {
    const theme = useTheme();
    return (
        <Box
            sx={{
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                textAlign: "center",
            }}
        >
            <Typography
                variant="h4"
                sx={{
                    textAlign: "center",
                    color: theme.palette.text.primary,
                    fontSize: isMobile ? "20px" : "38px",
                    fontWeight: isMobile && 600,
                    lineHeight: "116.7%",
                    mt: "0",
                }}
            >
                {PRESENTATION_HEADLINE}
            </Typography>
            <Avatar
                src="/globe.svg"
                alt="Globe Icon"
                sx={{
                    width: isMobile ? 30 : 38,
                    height: isMobile ? 30 : 38,
                    marginLeft: 1,
                    display: "inline-block",
                    verticalAlign: "middle",
                }}
            />
        </Box>
    );
};

export default ConversationHeader;
