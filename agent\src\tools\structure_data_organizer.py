"""Summary generation module for query results."""

from typing import Dict, Any, List, Optional, Union
from pydantic import BaseModel
import logging
from src.tools.base import Tool
from src.tools.entity_extractor import ExtractedEntities
import requests
import re
from jinja2 import Template
import os
import json
import google.auth
from google.auth.transport.requests import AuthorizedSession
from google.oauth2 import service_account

logger = logging.getLogger(__name__)

DATA_ANALYSIS_API_URL = "https://data-analysis-din4qs2qka-uc.a.run.app/analyze"
# DATA_ANALYSIS_API_URL = "http://localhost:12345/analyze"  # For local testing, change as needed


class DataAnalysisRequest(BaseModel):
    query: str
    entities: Optional[Dict[str, Any]] = None
    dataset: Optional[Union[List[Dict[str, Any]], str]] = None
    examples_file: Optional[str] = "structured_examples.json"

    def to_dict(self):
        request_dict = self.dict()
        if self.entities:
            request_dict["interventions"] = self.entities["interventions"]
            request_dict["outcomes"] = self.entities["outcomes"]
            del request_dict["entities"]
        return request_dict


class DataAnalyzer(Tool):
    """Tool for generating data analysis results."""

    def __init__(self, config: Dict[str, Any]):
        """Initialize the data analyzer."""
        super().__init__(
            name="data_analyzer",
            description="Generate data analysis results based on structured and unstructured data",
            func=self.generate,
            arguments=[
                ("user_query", "str"),
                ("entities", "Optional[ExtractedEntities]"),
                ("dataset", "Optional[Union[List[Dict[str, Any]], str]]"),
            ],
            outputs=[("text", "str")],
            config=config,
        )
        self.verbose = config.get("verbose", False)
        self.authed_session = self._setup_google_auth()

    def _setup_google_auth(self) -> Optional[AuthorizedSession]:
        """Set up Google Cloud authentication with fallback."""
        auth_methods = [
            lambda: self._try_env_credentials(),
            lambda: google.auth.default(
                scopes=["https://www.googleapis.com/auth/cloud-platform"]
            ),
        ]

        for method in auth_methods:
            try:
                credentials = method()
                if credentials:
                    return AuthorizedSession(
                        credentials[0]
                        if isinstance(credentials, tuple)
                        else credentials
                    )
            except Exception as e:
                if self.verbose:
                    logger.warning(f"Auth method failed: {e}")

        return None

    def _try_env_credentials(self):
        """Try to load credentials from environment variable."""
        creds_json = os.getenv("GOOGLE_APPLICATION_CREDENTIALS_JSON")
        if not creds_json:
            return None

        # Handle file path or JSON content
        if creds_json.startswith("/") or creds_json.endswith(".json"):
            return service_account.Credentials.from_service_account_file(
                creds_json, scopes=["https://www.googleapis.com/auth/cloud-platform"]
            )
        else:
            return service_account.Credentials.from_service_account_info(
                json.loads(creds_json),
                scopes=["https://www.googleapis.com/auth/cloud-platform"],
            )

    def _make_request(self, request_data: Dict[str, Any]) -> Dict[str, Any]:
        """Make API request with authentication fallback."""
        # Try authenticated request first
        if self.authed_session:
            try:
                response = self.authed_session.post(
                    DATA_ANALYSIS_API_URL,
                    json=request_data,
                    headers={"Content-Type": "application/json"},
                )
                response.raise_for_status()
                return response.json()
            except Exception as e:
                if self.verbose:
                    logger.warning(f"Authenticated request failed: {e}")

        # Fallback to unauthenticated
        if self.verbose:
            logger.info("Using unauthenticated request")
        response = requests.post(
            DATA_ANALYSIS_API_URL,
            json=request_data,
            headers={"Content-Type": "application/json"},
        )
        response.raise_for_status()
        return response.json()

    async def generate(
        self,
        user_query: str,
        entities: ExtractedEntities,
        dataset: Optional[List[Dict[str, Any]]] = None,
    ) -> Dict[str, Any]:
        """Generate data analysis results based on available data."""
        try:
            if self.verbose:
                logger.info(f"Generating data analysis for: {user_query}")

            request = DataAnalysisRequest(
                query=user_query,
                dataset=dataset,
                entities=(
                    {
                        "interventions": [
                            {"label": i.label, "mention": i.mention}
                            for i in (entities.interventions if entities else [])
                        ],
                        "outcomes": [
                            {"label": o.label, "mention": o.mention}
                            for o in (entities.outcomes if entities else [])
                        ],
                    }
                    if entities
                    else {}
                ),
                examples_file="structured_examples.json",
            )

            result = self._make_request(request.to_dict())

            if self.verbose:
                logger.info(f"Generated text length: {len(result.get('analysis', ''))}")

            return {
                "text": result.get("analysis", ""),
                "conversation_history": result.get("conversation_history", []),
                "error_message": result.get("error_message"),
                "url": result.get("url"),
            }

        except Exception as e:
            if self.verbose:
                logger.error(f"Error generating data analysis: {str(e)}")
            raise


class StructuredData(BaseModel):
    """Structured data with metadata."""

    text: dict | str
    conversation_history: List[Dict[str, Any]]
    error_message: Optional[str] = None
    url: Optional[str] = None

    def get_answer_text(self) -> str:
        """Get clean answer text."""
        try:
            return self.text

        except Exception as e:
            if self.verbose:
                logger.error(f"Error formatting answer text: {str(e)}")
            # Return cleaned raw text as fallback
            text = re.sub(r"\s+", " ", self.text.strip())
            return text


class StructuredDataOrganizer(Tool):
    """Tool for generating data analysis results."""

    def __init__(self, config: Dict[str, Any]):
        """Initialize the data analyzer."""
        super().__init__(
            name="structured_data_organizer",
            description="Organize structured data into a data analysis results to get figures about impact and effect of interventions",
            func=self.generate,
            arguments=[
                ("user_query", "str"),
                ("entities", "Optional[ExtractedEntities]"),
                ("dataset", "Optional[Union[List[Dict[str, Any]], str]]"),
            ],
            outputs=[
                (
                    "structured_data",
                    "StructuredData(text: dict | str, conversation_history: List[Dict[str, Any]], error_message: Optional[str], url: Optional[str])",
                )
            ],
            config=config,
        )
        self.verbose = config.get("verbose", False)
        self.data_analyzer = DataAnalyzer(config)

    async def generate(
        self,
        user_query: str,
        entities: ExtractedEntities,
        dataset: Optional[Union[List[Dict[str, Any]], str]] = None,
    ) -> StructuredData:
        """Generate data analysis results."""
        try:
            # Generate data analysis results
            result = await self.data_analyzer.generate(
                user_query=user_query, entities=entities, dataset=dataset
            )

            text = result["text"]
            conversation_history = result["conversation_history"]
            error_message = result["error_message"]
            url = result["url"]
            text = Template(text).render(user_query=user_query)

            return StructuredData(
                text=text,
                conversation_history=conversation_history,
                error_message=error_message,
                url=url,
            )

        except Exception as e:
            if self.verbose:
                logger.error(f"Error generating structured data: {str(e)}")
            raise
