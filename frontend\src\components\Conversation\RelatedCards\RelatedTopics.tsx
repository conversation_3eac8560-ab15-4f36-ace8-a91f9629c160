import React from 'react';
import {
    Box,
    Chip,
} from '@mui/material';
import { useTheme } from '@mui/material/styles';
import { Option } from "../../../types/ConversationTypes";

interface RelatedTopicsProps {
    options: Option[];
    handleChange: (option: Option, type: string) => void;
    userWaitingForResponse?: boolean;
}

const RelatedTopics: React.FC<RelatedTopicsProps> = ({
    options,
    handleChange,
    userWaitingForResponse = false,
}) => {
    const theme = useTheme();
    if (!options || options.length === 0) {
        return null;
    }

    return (
        <Box
            display="flex"
            flexWrap="wrap"
            gap={1}
            width="100%"
            justifyContent="flex-start"
            alignItems="flex-start"
            flexDirection="column"
        >
            {options.map((option, index) => (
                <Chip
                    key={`${option.ref}_${index}`}
                    label={option.description}
                    onClick={() => !userWaitingForResponse && handleChange(option, 'suggested_topics')}
                    sx={{
                        height: 'auto',
                        display: 'inline-flex',
                        padding: '4px',
                        justifyContent: 'center',
                        alignItems: 'flex-start',
                        flexShrink: 0,
                        borderRadius: '100px',
                        border: `1px solid ${theme.components.input.outlined.enabledBorder}`,
                        backgroundColor: 'transparent',
                        color: theme.palette.text.primary,
                        cursor: userWaitingForResponse ? 'not-allowed' : 'pointer',
                        pointerEvents: userWaitingForResponse ? 'none' : 'auto',
                        transition: 'background-color 0.3s ease, border-color 0.3s ease',
                        '&:hover': {
                            backgroundColor: userWaitingForResponse ? 'transparent' : theme.components.input.filled.enabledFill,
                            borderColor: userWaitingForResponse ? theme.palette.divider : theme.components.input.outlined.hoverBorder,
                        },
                        '& .MuiChip-label': {
                            fontSize: '16px',
                            color: theme.palette.text.secondary,
                            whiteSpace: 'normal',
                            wordBreak: 'break-word',
                            height: 'auto'
                        },
                    }}
                />
            ))}
        </Box>
    );
};

export default RelatedTopics;