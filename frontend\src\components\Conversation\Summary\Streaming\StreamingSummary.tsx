import React, { useState, useEffect, useRef, useCallback, useContext } from "react";
import { BASE_URL } from "../../../../services/apiService";
import { Message } from "../../../../types/ConversationTypes";
import { Box } from '@mui/material';
import MemoizedMarkdown from "./MemoizedMarkdown";
import StaticMarkdown from "./StaticMarkdown";
import { NormalizeSpaces } from "./Utils";
import { LayoutContext } from "../../../Layout/LayoutContext";
import SystemLoadingIndicator from "./SystemLoadingIndicator";
import usePlotAndSourcesData from "../../../../hooks/usePlotAndSourceData";


interface StreamingSummaryProps {
  conversationId: string;
  informationMessageId: string;
  setDisplaySystemLoader: (flag: boolean) => void;
  setStreamingEnded: (flag: boolean) => void;
  setSummaryStreamedText: (text: string | null) => void;
  onViewOnPlotClicked: (payload: { citation_ids: { key: string; value: string }[]; messageId: string; plotDataInfo?: any }) => void;
  onViewOnSourceClicked: (payload: { paper_ids: string[]; messageId: string }) => void;
  messageId: string;
  setDisplayText: (text: string | null) => void;
  setFullMessageInfoFetched: (message: Message) => void;
}

const StreamingSummary: React.FC<StreamingSummaryProps> = ({
  conversationId,
  informationMessageId,
  setDisplaySystemLoader,
  setStreamingEnded,
  setSummaryStreamedText,
  onViewOnPlotClicked,
  onViewOnSourceClicked,
  messageId,
  setDisplayText,
  setFullMessageInfoFetched
}) => {
  const {
    streamingError,
    updateStreamingErrorState
  } = useContext(LayoutContext);
  const [displayChunks, setDisplayChunks] = useState<any[]>([]);
  const chunkQueueRef = useRef<any[]>([]);
  const lastChunkIdRef = useRef<string | null>(null);
  const processingChunkRef = useRef(false);
  const MAX_RETRIES = 1;
  const [hasStreamingStarted, setHasStreamingStarted] = useState(false);
  const [hasReceivedText, setHasReceivedText] = useState(false);
  const [currentTextLength, setCurrentTextLength] = useState(0);
  const accumulatedTextRef = useRef("");

  const {
    fullMessageInfo,
    hasAttemptedGetInfoFetch,
  } = usePlotAndSourcesData({
    conversationId: conversationId,
    informationMessageId: informationMessageId,
    hasStreamingStartedForDataFetch: hasStreamingStarted,
  });


  const processNextChunk = useCallback(() => {
    if (chunkQueueRef.current.length > 0 && !processingChunkRef.current) {
      processingChunkRef.current = true;
      const nextChunk = chunkQueueRef.current.shift();
      if (nextChunk) {
        setDisplayChunks(prevChunks => [...prevChunks, { ...nextChunk, shouldAnimate: true }]);
        const newTextLength = (nextChunk.text || "").trim().length;
        if (newTextLength > 0 && !hasReceivedText && !hasStreamingStarted) {
          setHasStreamingStarted(true);
        }
        if (newTextLength > 0 && !hasReceivedText) {
          setHasReceivedText(true);
        }
        setCurrentTextLength(prevLength => prevLength + newTextLength);
        accumulatedTextRef.current += nextChunk.text || "";
      }
    }
  }, [hasReceivedText]);

  useEffect(() => {
    if (conversationId && informationMessageId) {
      setDisplayChunks([]);
      setHasStreamingStarted(false);
      setHasReceivedText(false);
      setCurrentTextLength(0);
      chunkQueueRef.current = [];
      lastChunkIdRef.current = null;
      processingChunkRef.current = false;
      accumulatedTextRef.current = "";
      fetchSummaryData();
    }
  }, [conversationId, informationMessageId]);

  const handleRetryOrError = async (retryCount: number) => {
    if (retryCount < MAX_RETRIES) {
      setDisplaySystemLoader(true);
      await new Promise(res => setTimeout(res, 1000));
      fetchSummaryData(retryCount + 1);
    } else {
      setDisplaySystemLoader(false);
      setStreamingEnded(true);
      updateStreamingErrorState(true);
      setSummaryStreamedText(accumulatedTextRef.current);
    }
  };

  const fetchSummaryData = async (retryCount = 0) => {
    try {
      setDisplaySystemLoader(true);
      const token = localStorage.getItem("token");
      const response = await fetch(
        `${BASE_URL}/conversations/${conversationId}/messages/${informationMessageId}/summary`,
        {
          headers: {
            'Cache-Control': 'no-cache',
            Authorization: token ? `Bearer ${token}` : "",
          }
        }
      );
      if (response.status === 200 && response.body) {
        const reader = response.body.getReader();
        const decoder = new TextDecoder("utf-8");
        let done = false;
        let buffer = "";
        let localChunkId = 0;
        let hasContent = false;

        while (!done) {
          const { value, done: streamDone } = await reader.read();
          done = streamDone;

          if (value) {
            hasContent = true;
            let chunk = decoder.decode(value, { stream: true });
            buffer += chunk;

            const lines = buffer.split(/(?:\n{1,2}|<hr\s*\/?>|-{3,}|\*{3,}|_{3,})/i).filter(Boolean);
            buffer = lines.pop() || "";

            if (lines.length > 0) {
              let processedChunks = lines.map((line) => {
                const normalizedText = NormalizeSpaces(line.trim());
                return {
                  id: `chunk_${localChunkId++}`,
                  text: normalizedText + '\n',
                  shouldAnimate: false,
                };
              });

              chunkQueueRef.current.push(...processedChunks);
              processNextChunk();
            }

            const normalizedChunk = NormalizeSpaces(chunk);
            setDisplayText((prev) => (prev || "") + normalizedChunk);
            const normalizedChunkLength = normalizedChunk.trim().length;
            if (normalizedChunkLength > 0 && !hasReceivedText && !hasStreamingStarted) {
              setHasStreamingStarted(true);
            }
            if (normalizedChunkLength > 0 && !hasReceivedText) {
              setHasReceivedText(true);
            }
            setCurrentTextLength(prevLength => prevLength + normalizedChunkLength);
          }
        }

        if (!hasContent) {
          handleRetryOrError(retryCount);
          return;
        }

        if (buffer.trim()) {
          const trimmedBuffer = NormalizeSpaces(buffer.trim());
          const finalChunk = {
            id: `chunk_${localChunkId++}`,
            text: trimmedBuffer + '\n\n',
            shouldAnimate: false,
          };
          chunkQueueRef.current.push(finalChunk);
          lastChunkIdRef.current = finalChunk.id;
          processNextChunk();
          const trimmedBufferLength = trimmedBuffer.length;
          if (trimmedBufferLength > 0 && !hasReceivedText && !hasStreamingStarted) {
            setHasStreamingStarted(true);
          }
          if (trimmedBufferLength > 0 && !hasReceivedText) {
            setHasReceivedText(true);
          }
          setCurrentTextLength(prevLength => prevLength + trimmedBufferLength);
        }
        setDisplaySystemLoader(false);
      } else {
        handleRetryOrError(retryCount);
      }
    } catch (error) {
      console.error("Error fetching summary:", error);
      handleRetryOrError(retryCount);
    }
  };

  useEffect(() => {
    if (hasAttemptedGetInfoFetch) {
      setFullMessageInfoFetched(fullMessageInfo);
      if (!hasReceivedText) {
        setHasStreamingStarted(false);
        setHasReceivedText(false);
        setCurrentTextLength(0);
        chunkQueueRef.current = [];
        lastChunkIdRef.current = null;
        processingChunkRef.current = false;
        accumulatedTextRef.current = "";
      }
      if (fullMessageInfo?.sources === null && fullMessageInfo?.plot === null) {
        setDisplaySystemLoader(false);
      }
    }
  },
    [hasAttemptedGetInfoFetch,
      fullMessageInfo,
      setFullMessageInfoFetched,
      hasReceivedText,
      setDisplaySystemLoader
    ]);


  const onChunkComplete = useCallback((completedChunkId: string) => {
    setDisplayChunks(prevChunks =>
      prevChunks.map(chunk =>
        chunk.id === completedChunkId ? { ...chunk, shouldAnimate: false } : chunk
      )
    );
    processingChunkRef.current = false;
    if (completedChunkId === lastChunkIdRef.current) {
      setStreamingEnded(true);
      setSummaryStreamedText(accumulatedTextRef.current);
    }
    processNextChunk();
  }, [processNextChunk, setSummaryStreamedText]);

  if (streamingError) {
    return null;
  }

  const shouldFlexStart = currentTextLength >= 100;
  const showInitialLoadingState = !hasReceivedText && !hasAttemptedGetInfoFetch;

  return (
    <Box sx={{
      display: 'flex',
      alignItems: showInitialLoadingState ? 'flex-start' : (shouldFlexStart ? 'flex-start' : 'center')
    }}>
      <Box
        sx={{
          width: 32,
          height: 32,
          marginRight: 2,
          flexShrink: 0,
          backgroundImage: showInitialLoadingState || !hasAttemptedGetInfoFetch ? `url('/ImpactAI.svg')` : `url('/ImpactAI_Static_Logo.svg')`,
          backgroundSize: 'contain',
          backgroundRepeat: 'no-repeat',
          display: showInitialLoadingState || displayChunks.length > 0 || hasAttemptedGetInfoFetch ? 'block' : 'none',
        }}
      />
      <Box sx={{ flexGrow: 1 }} className="markdown-container">
        {showInitialLoadingState || !hasAttemptedGetInfoFetch ? (
          <SystemLoadingIndicator isLoading={true} />
        ) : (
          displayChunks.map((chunk) => (
            chunk.shouldAnimate ? (
              <MemoizedMarkdown
                key={chunk.id}
                text={chunk.text}
                onComplete={() => onChunkComplete(chunk.id)}
                sources={fullMessageInfo?.has_sources ? (fullMessageInfo.sources || []) : []}
                plotData={fullMessageInfo?.has_plot ? (fullMessageInfo.plot || null) : null}
                onViewOnPlotClicked={onViewOnPlotClicked}
                onViewOnSourceClicked={onViewOnSourceClicked}
                messageId={messageId}
              />
            ) : (
              <StaticMarkdown
                key={chunk.id}
                text={chunk.text}
                sources={fullMessageInfo?.has_sources ? (fullMessageInfo.sources || []) : []}
                plotData={fullMessageInfo?.has_plot ? (fullMessageInfo.plot || null) : null}
                onViewOnPlotClicked={onViewOnPlotClicked}
                onViewOnSourceClicked={onViewOnSourceClicked}
                messageId={messageId}
              />
            )
          ))
        )}
      </Box>
    </Box>
  );
};

export default StreamingSummary;