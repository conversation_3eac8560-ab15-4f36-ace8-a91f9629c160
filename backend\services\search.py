from utils.requests import get_json
from typing import Any


class SearchService:
    async def get_chips(self) -> list[Any]:
        response = await get_json(
            url="https://chips-564807556547.us-central1.run.app/get_chips?include_region=true&include_country=true&include_target_pop=true&k=5"
        )
        chips = []
        for intervention in response['intervention']:
            chips.append({
                "type": "intervention",
                "value": intervention["question"],
                "label": intervention["question"],
            })
        for outcome in response['outcome']:
            chips.append({
                "type": "outcome",
                "value": outcome["question"],
                "label": outcome["question"],
            })
        return chips