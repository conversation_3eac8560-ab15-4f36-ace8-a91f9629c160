export const APPLICATION_TITLE = 'Causal Chat Interface';
export const NEW_CHAT_BUTTON_LABEL = 'New Chat';
export const DEFAULT_BUTTON_LABEL = 'Default Button';
export const DEFAULT_CHAT_HISTORY_LABEL = 'Chat History';
export const NO_CHATS_HISTORY_FOUND_LABEL = 'No chat history available';
export const DEFAULT_CHAT_QUERY_PLACEHOLDER_LABEL = 'Explore the universe of impact evaluations...';
export const DEFAULT_CHAT_QUERY_PLACEHOLDER_FOLLOW_UP_LABEL = 'Ask a follow up question...';
export const DEFAULT_CHAT_QUERY_SECTION_HEADLINE_LABEL = 'Where better policy starts';
export const DOWNLOAD_BUTTON_LABEL = 'DOWNLOAD CSV';
export const NO_CHAT_ACTIVE_SESSION_FOUND = "It seems you don't have an active chat. Start a new one now.";
export const ERROR_INITIALISING_CHAT = "An error occurred while initializing the chat. Please try again later.";
export const ERROR_CHAT_NOT_FOUND = "An error occurred while getting the chat information. Please try again later.";
export const EXPLORE_TOPIC_LABEL = 'Explore a topic...';
export const ASK_FOLLOW_UP_QUESTION_LABEL = 'Ask a follow up question...';
export const PRESENTATION_HEADLINE = 'Bringing evidence to scale';
export const RELATED_TOPICS_TITLE = 'Related';
export const SUGGESTED_TOPICS_TITLE = 'Suggested Topics';
export const GRAPH_EXPLORATION_LABEL = 'Graphical exploration';
export const GRAPH_FOOTNOTE_LABEL = 'Standardized Mean Difference ± 95% Confidence Interval';
export const GRAPH_FOOTNOTE_LABEL1 = 'Effect Size (In Standardized Mean Difference)';
export const GRAPH_FOOTNOTE_LABEL2 = '+- 95% Confidence Interval';
export const PROMPT_LABEL = 'Ask ImpactAI...';
export const SEARCH_PROMPT_LABEL_0 = 'Search for an intervention...';
export const SEARCH_PROMPT_LABEL_1 = 'Search for an outcome...';
export const VIEW_GRAPH_ICON_LABEL = 'View on graph';
export const VIEW_SOURCE_ICON_LABEL = 'View source';
export const LANDING_PAGE_ALERT_TEXT = 'ImpactAI is currently in beta. Our AI models are working with limited data from development economics, therefore the answers provided may not always be comprehensive.';
export const API_ERROR_MESSAGE = 'We are currently fixing some issues, please come back later!';
export const CHAT_HISTORY_API_ERROR_MESSAGE = 'Cannot fetch chat history items at the moment, please try again';
export const THUMBS_FEEDBACK_GOOD_RESPONSE_LABEL = 'Good response';
export const THUMBS_FEEDBACK_BAD_RESPONSE_LABEL = 'Bad response';
