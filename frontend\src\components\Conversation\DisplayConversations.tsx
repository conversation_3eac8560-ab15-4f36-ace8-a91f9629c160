import React, { useEffect, useState, useRef, useContext, useMemo, useCallback } from "react";
import { Grid, Box, Button } from "@mui/material";
import { useTheme } from "@mui/material/styles";
import { useIsMobile, useIsTablet } from "../Layout/MobileUtils";
import { Message, Source } from "../../types/ConversationTypes";
import UserAnswerDisplay from "./User/UserAnswerDisplay";
import SummarySection from "./Summary/SummarySection";
import ComparisonView from "./ComparisonView/ComparisonView";
import ThumbsFeedback from "./Feedback/ThumbsFeedback";
import PromptSection from "../PromptControl/PromptSection";
import { PROMPT_LABEL } from "../../utils/labels";
import TravelExploreIcon from '@mui/icons-material/TravelExplore';
import Sources from "./Sources/Sources";
import { LayoutContext } from "../Layout/LayoutContext";
import useScrollbarWidth from "../../hooks/useScrollbarWidth";
import useChatScroll from '../../hooks/useChatScroll';
import { CenteredPageLoader } from "./LoadingState";
import RelatedTopics from "./RelatedCards/RelatedTopics";
import { hasContent } from '../../utils/dataUtils';
import ShowChartIcon from '@mui/icons-material/ShowChart';

interface DisplayConversationProps {
    loadingMessages: boolean;
    localConversationId: string;
    displaySystemLoader: boolean;
    setDisplaySystemLoader: (flag: boolean) => void;
    streamingEnded: boolean;
    setStreamingEnded: (flag: boolean) => void;
    handleUserPrompt: (message: any) => void;
    informationMessageId: string | null;
    onScrollComplete: () => void;
    newQuestionId: string;
    initialConversationMessages: Message[];
    summaryStreamedText: string;
    setSummaryStreamedText: (text: string) => void;
    setLocalMessageList: (messages: Message[]) => void;
    forceScrollToEnd: boolean;
    onForceScrollComplete: () => void;
}

const DisplayConversation: React.FC<DisplayConversationProps> = ({
    loadingMessages,
    localConversationId,
    displaySystemLoader,
    setDisplaySystemLoader,
    streamingEnded,
    setStreamingEnded,
    handleUserPrompt,
    informationMessageId,
    onScrollComplete,
    newQuestionId,
    initialConversationMessages,
    summaryStreamedText,
    setSummaryStreamedText,
    setLocalMessageList,
    forceScrollToEnd,
    onForceScrollComplete
}) => {
    const theme = useTheme();
    const isMobile = useIsMobile();
    const isTablet = useIsTablet();
    const isMobileOrTablet = isMobile || isTablet;
    const [selectedStudy, setSelectedStudy] = useState("");
    const [activeQuestionId, setActiveQuestionId] = useState<string | null>(null);
    const [hoveredMessageId, setHoveredMessageId] = useState<string | null>(null);
    const lastMessageRef = useRef<HTMLDivElement | null>(null);
    const containerRef = useRef<HTMLDivElement | null>(null);
    const leftColumnScrollRef = useRef<HTMLDivElement>(null);
    const conversationWrapperScrollRef = useRef<HTMLDivElement>(null);
    const [openRightSection, setOpenRightSection] = useState<"sources" | "charts" | null>(null);
    const [activeInfoMessageIdForPanel, setActiveInfoMessageIdForPanel] = useState<string | null>(null);
    const [panelSources, setPanelSources] = useState<Source[]>([]);
    const {
        updateActiveMessageInfo,
        isSidebarCollapsed
    } = useContext(LayoutContext);
    const scrollbarWidth = useScrollbarWidth();
    const [fullMessageInfoFetched, setFullMessageInfoFetched] = useState<Message | null>(null);
    const [previousScrollTop, setPreviousScrollTop] = useState(0);
    const [activePlotDetails, setActivePlotDetails] = useState<{
        citation_ids: { key: string; value: string }[];
        messageId: string;
        plotDataInfo?: any;
    } | null>(null);
    const [activeSourcePaperIds, setActiveSourcePaperIds] = useState<string[]>([]);
    const [activeSourceMessageId, setActiveSourceMessageId] = useState<string | null>(null);

    useChatScroll({
        scrollContainerRef: openRightSection ? leftColumnScrollRef : conversationWrapperScrollRef,
        displaySystemLoader: displaySystemLoader,
        forceScrollToEnd: forceScrollToEnd,
        onForceScrollComplete: onForceScrollComplete
    });

    const commonFocusHoverActiveStyles = {
        border: `1px solid ${theme.components.input.standard.hoverBorder}`,
        color: theme.palette.text.primary,
        backgroundColor: theme.palette.common.white,
    };

    const buttonStyle = {
        color: theme.palette.text.secondary,
        fontSize: '13px',
        fontStyle: 'normal',
        fontWeight: 600,
        lineHeight: '22px',
        letterSpacing: '1px',
        textTransform: 'uppercase',
        height: '30px',
        "&:hover": {
            ...commonFocusHoverActiveStyles,
        },
    };

    const handleCloseRightSection = useCallback(() => {
        const currentScrollPositionInLeftColumn = leftColumnScrollRef.current?.scrollTop || 0;
        setOpenRightSection(null);
        setActiveInfoMessageIdForPanel(null);
        updateActiveMessageInfo(null);
        setPanelSources([]);
        setActivePlotDetails(null);
        setActiveSourcePaperIds([]);
        setActiveSourceMessageId(null);
        setSelectedStudy("");

        requestAnimationFrame(() => {
            if (conversationWrapperScrollRef.current) {
                conversationWrapperScrollRef.current.scrollTop = currentScrollPositionInLeftColumn;
                setPreviousScrollTop(0);
            }
        });
    }, [
        leftColumnScrollRef,
        conversationWrapperScrollRef,
        updateActiveMessageInfo
    ]);

    useEffect(() => {
        if (localConversationId) {
            handleCloseRightSection();
        }
    }, [localConversationId]);

    useEffect(() => {
        if (newQuestionId) {
            setActiveQuestionId(newQuestionId);
        }
    }, [newQuestionId]);

    useEffect(() => {
        if (informationMessageId) {
            setLocalMessageList(prevMessages =>
                prevMessages.map(msg => {
                    if (msg.id === 'system-loading') {
                        const updatedText = streamingEnded
                            ? (fullMessageInfoFetched?.text || msg.text)
                            : summaryStreamedText;

                        const updatedSources = (streamingEnded && fullMessageInfoFetched?.sources !== undefined)
                            ? fullMessageInfoFetched.sources
                            : msg.sources;

                        const updatedPlot = (streamingEnded && fullMessageInfoFetched?.plot !== undefined)
                            ? fullMessageInfoFetched.plot
                            : msg.plot;

                        const updatedTopics = (streamingEnded && fullMessageInfoFetched?.choices !== undefined)
                            ? fullMessageInfoFetched.choices
                            : msg.choices;

                        const updatedHasSources = (streamingEnded && fullMessageInfoFetched?.has_sources !== undefined)
                            ? fullMessageInfoFetched.has_sources
                            : msg.has_sources;

                        const updatedHasPlot = (streamingEnded && fullMessageInfoFetched?.has_plot !== undefined)
                            ? fullMessageInfoFetched.has_plot
                            : msg.has_plot;

                        return {
                            ...msg,
                            sources: updatedSources,
                            plot: updatedPlot,
                            text: updatedText,
                            choices: updatedTopics,
                            has_sources: updatedHasSources,
                            has_plot: updatedHasPlot
                        };
                    }
                    return msg;
                })
            );

            if (streamingEnded && informationMessageId) {
                if (openRightSection === "sources") {
                    if (fullMessageInfoFetched?.sources && fullMessageInfoFetched.sources.length > 0) {
                        setPanelSources(fullMessageInfoFetched.sources);
                    }
                }
            }
        }
    }, [
        summaryStreamedText,
        fullMessageInfoFetched,
        informationMessageId,
        streamingEnded,
        openRightSection,
        setLocalMessageList
    ]);

    useEffect(() => {
        if (activeQuestionId && localConversationId) {
            setTimeout(() => {
                const activeQuestionElement = document.getElementById(activeQuestionId);
                const container = containerRef.current;
                if (activeQuestionElement && container) {
                    const messageTop = activeQuestionElement.offsetTop;
                    const messageBottom = messageTop + activeQuestionElement.offsetHeight;
                    const visibleTop = container.scrollTop;
                    const visibleBottom = visibleTop + container.clientHeight;
                    if (messageBottom > visibleBottom || messageTop < visibleTop) {
                        activeQuestionElement.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start',
                        });
                    }
                    requestAnimationFrame(() => {
                        setTimeout(() => {
                            onScrollComplete();
                        }, 500);
                    });
                } else {
                    console.error('Active question element or container not found for scroll.');
                }
            }, 0);
        }
    }, [activeQuestionId, localConversationId]);

    const handleOpenCharts = (messageId: string, plotPayload?: { citation_ids: { key: string; value: string }[]; messageId: string; plotDataInfo?: any; } | null) => {
        if (conversationWrapperScrollRef.current) {
            setPreviousScrollTop(conversationWrapperScrollRef.current.scrollTop);
        }
        setOpenRightSection("charts");
        setActiveInfoMessageIdForPanel(messageId);
        const clickedMessage = initialConversationMessages.find(msg => msg.id === messageId);
        setSelectedStudy("");
        let sourcesToSet: Source[] = [];
        if (messageId === informationMessageId && fullMessageInfoFetched && fullMessageInfoFetched.sources) {
            sourcesToSet = fullMessageInfoFetched.sources;
        } else if (clickedMessage?.sources) {
            sourcesToSet = clickedMessage.sources;
        }

        if (plotPayload && hasContent(plotPayload.plotDataInfo)) {
            setActivePlotDetails({
                citation_ids: plotPayload.citation_ids || [],
                messageId: messageId,
                plotDataInfo: plotPayload.plotDataInfo
            });
        } else if (clickedMessage?.plot && hasContent(clickedMessage.plot)) {
            setActivePlotDetails({
                citation_ids: [],
                messageId: messageId,
                plotDataInfo: clickedMessage.plot
            });
        } else {
            setActivePlotDetails(null);
        }

        setPanelSources(sourcesToSet);
        updateActiveMessageInfo(clickedMessage);
    };

    const handleOpenSources = (messageId: string, fromSourcesButton = false) => {
        if (conversationWrapperScrollRef.current) {
            setPreviousScrollTop(conversationWrapperScrollRef.current.scrollTop);
        }
        setOpenRightSection("sources");
        setActiveInfoMessageIdForPanel(messageId);
        setSelectedStudy("");
        const clickedMessage = initialConversationMessages.find(m => m.id === messageId);
        setPanelSources(clickedMessage?.sources || []);

        if (fromSourcesButton) {
            setActiveSourcePaperIds([]);
            setActiveSourceMessageId(null);
        }
        updateActiveMessageInfo(clickedMessage || null);
    };

    const handleViewOnPlotClickedInternal = useCallback((payload: {
        citation_ids: { key: string; value: string }[];
        messageId: string;
        plotDataInfo?: any;
    }) => {
        if (hasContent(payload.plotDataInfo)) {
            handleOpenCharts(payload.messageId, payload);
        }
    }, [handleOpenCharts]);

    const handleViewOnSourceClickedInternal = useCallback((payload: { paper_ids: string[]; messageId: string }) => {
        const clickedMessage = initialConversationMessages.find(m => m.id === payload.messageId);
        if (clickedMessage?.sources && clickedMessage.sources.length > 0 && clickedMessage?.has_sources) {
            const messageSourcePaperIds = new Set(clickedMessage.sources.map(source => source.short_paper_id));
            const hasMatchingPaperId = payload.paper_ids.some(paperId =>
                messageSourcePaperIds.has(paperId)
            );
            if (hasMatchingPaperId) {
                setActiveSourcePaperIds(payload.paper_ids);
                setActiveSourceMessageId(payload.messageId);
                handleOpenSources(payload.messageId, false);
            }
        }
    }, [handleOpenSources]);

    useEffect(() => {
        if (openRightSection && leftColumnScrollRef.current && previousScrollTop !== 0) {
            leftColumnScrollRef.current.scrollTop = previousScrollTop;
        }
    }, [openRightSection, previousScrollTop]);

    const isTwoColumnLayoutWithPanelOpen = useMemo(() => {
        return !isMobileOrTablet && openRightSection !== null;
    }, [isMobileOrTablet, openRightSection]);

    const lastSystemMessageId = useMemo(() => {
        const validMessages = initialConversationMessages.filter(msg =>
            (msg !== null && msg !== undefined) || (msg?.id === 'system-loading' && !msg?.text)
        );
        const lastSystemMessage = [...validMessages]
            .reverse()
            .find(msg =>
                msg.type === 'information' &&
                msg.author === 'system' &&
                (
                    (msg?.text !== null && msg?.text !== undefined) ||
                    (informationMessageId && msg?.id === 'system-loading')
                )
            );
        return lastSystemMessage?.id || null;
    }, [initialConversationMessages]);

    const renderMessageContent = (msg: Message, index: number) => {
        const isCurrentUserMessage = msg.author === 'user' && msg.type === 'answer' && msg?.text && msg?.text.length > 0;
        const displaySourcesButton = msg?.has_sources || false;
        const displayChartsButton = msg?.has_plot || false;
        const shouldFlexStart = msg?.text && msg?.text.length >= 100;
        const isLastSystemMsg = msg.id === lastSystemMessageId;
        const isCurrentInfoMsg = 'system-loading' === msg.id;

        let feedbackText = "";
        let isFeedbackVisible = false;
        let isSourcesChartsVisible = false;
        let isRelatedTopicsVisible = false;

        const showOnHover = msg.type === 'information' && msg.author === 'system' && !!msg?.text && (displaySourcesButton || displayChartsButton || !!msg?.text) && msg.id === hoveredMessageId;

        if (!informationMessageId) {
            feedbackText = msg?.text || "";
            isFeedbackVisible = (isLastSystemMsg && !!msg?.text && !displaySystemLoader) || showOnHover;
            isSourcesChartsVisible = (isLastSystemMsg && !!msg?.text && !displaySystemLoader && (displaySourcesButton || displayChartsButton)) || showOnHover;
            isRelatedTopicsVisible = ((msg.choices || []).length > 0);
        }
        else if (isCurrentInfoMsg) {
            feedbackText = streamingEnded && !displaySystemLoader && !!summaryStreamedText ? summaryStreamedText : "";
            isFeedbackVisible = (streamingEnded && !!summaryStreamedText) || showOnHover;
            isSourcesChartsVisible = (streamingEnded && !!summaryStreamedText && (displaySourcesButton || displayChartsButton)) || showOnHover;
            isRelatedTopicsVisible = (streamingEnded && !!summaryStreamedText && (msg.choices || []).length > 0);
        }
        else if (msg.type === 'information' && msg.author === 'system' && !!msg?.text) {
            feedbackText = msg?.text || "";
            isFeedbackVisible = showOnHover;
            isSourcesChartsVisible = (displaySourcesButton || displayChartsButton) && showOnHover;
            isRelatedTopicsVisible = ((msg.choices || []).length > 0);
        }

        return (
            <Box
                key={msg.id}
                id={msg.id || ''}
                ref={index === initialConversationMessages.length - 1 && activeQuestionId ? lastMessageRef : null}
                className={`message-wrapper ${msg.id === activeQuestionId ? 'active-message' : ''}`}
                sx={{
                    display: 'flex',
                    alignItems: 'flex-start',
                    flexDirection: 'column',
                    justifyContent: shouldFlexStart ? 'flex-start' : 'center',
                }}
            >

                {(isCurrentUserMessage) && (
                    <Box sx={{ width: '100%' }}>
                        <UserAnswerDisplay
                            key={msg.id}
                            answer={msg}
                            isTwoColumnLayoutWithPanelOpen={isTwoColumnLayoutWithPanelOpen}
                        />
                        <Box sx={{ height: '40px' }} />
                    </Box>
                )}
                {msg.type === 'information' && msg.author === 'system' && (
                    <Box
                        onMouseEnter={() => setHoveredMessageId(msg.id)}
                        onMouseLeave={() => setHoveredMessageId(null)}
                        sx={{ width: '100%' }}
                        className="system-response"
                    >
                        <SummarySection
                            key={msg.id}
                            messageId={index === initialConversationMessages.length - 1 && informationMessageId ? informationMessageId : msg.id}
                            summary={msg}
                            conversationId={localConversationId}
                            informationMessageId={informationMessageId}
                            onViewOnPlotClicked={handleViewOnPlotClickedInternal}
                            onViewOnSourceClicked={handleViewOnSourceClickedInternal}
                            setDisplaySystemLoader={setDisplaySystemLoader}
                            setStreamingEnded={setStreamingEnded}
                            setSummaryStreamedText={setSummaryStreamedText}
                            setFullMessageInfoFetched={setFullMessageInfoFetched}
                        />
                        {isRelatedTopicsVisible && (
                            <Box id="related_topics"
                                sx={{
                                    paddingLeft: isMobileOrTablet ? '0px' : '48px',
                                    my: '16px',
                                    width: '100%',
                                    opacity: isRelatedTopicsVisible ? 1 : 0,
                                    pointerEvents: isRelatedTopicsVisible ? 'auto' : 'none',
                                    transition: 'opacity 0.3s ease-in-out, margin 0.3s ease-in-out',
                                }}
                            >
                                <RelatedTopics
                                    options={msg?.choices}
                                    handleChange={handleUserPrompt}
                                    userWaitingForResponse={!streamingEnded && displaySystemLoader}
                                />
                            </Box>
                        )}

                        {((displaySourcesButton || displayChartsButton) || !!feedbackText) ? (
                            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mt: 0 }}>
                                {(displaySourcesButton || displayChartsButton) && (
                                    <Box sx={{ display: 'flex', gap: isMobile ? '8px' : '16px', paddingLeft: isMobileOrTablet ? '0px' : '48px', opacity: isSourcesChartsVisible ? 1 : 0, pointerEvents: isSourcesChartsVisible ? 'auto' : 'none', transition: 'opacity 0.2s ease-in-out' }}>
                                        {displaySourcesButton && (
                                            <Button
                                                variant="text"
                                                size="small"
                                                startIcon={<TravelExploreIcon />}
                                                onClick={() => handleOpenSources(msg.id, true)}
                                                sx={{
                                                    display: 'inline-flex',
                                                    padding: '4px 10px',
                                                    justifyContent: 'center',
                                                    alignItems: 'center',
                                                    borderRadius: '8px',
                                                    border: `1px solid ${theme.components.input.outlined.disabledBorder}`,
                                                    background: '#FFF',
                                                    color: theme.palette.primary.main,
                                                    ...buttonStyle,
                                                }}
                                            >
                                                Sources
                                            </Button>
                                        )}
                                        {displayChartsButton && (
                                            <Button
                                                variant="text"
                                                size="small"
                                                startIcon={<ShowChartIcon />}
                                                onClick={() => handleOpenCharts(msg.id, null)}
                                                sx={{
                                                    display: 'inline-flex',
                                                    padding: '4px 10px',
                                                    justifyContent: 'center',
                                                    alignItems: 'center',
                                                    borderRadius: '8px',
                                                    border: `1px solid ${theme.components.input.outlined.disabledBorder}`,
                                                    background: '#FFF',
                                                    color: theme.palette.primary.main,
                                                    ...buttonStyle,
                                                }}
                                            >
                                                Charts
                                            </Button>
                                        )}
                                    </Box>
                                )}
                                {!!feedbackText && (
                                    <Box sx={{ marginLeft: 'auto' }}>
                                        <ThumbsFeedback
                                            conversationId={localConversationId}
                                            messageId={msg.id}
                                            visible={isFeedbackVisible}
                                            textToCopy={feedbackText}
                                        />
                                    </Box>
                                )}
                            </Box>
                        ) : null}
                    </Box>
                )}
            </Box>
        );
    }

    const renderSingleColumnLayout = () => (
        <Box
            ref={conversationWrapperScrollRef}
            className='conversation-wrapper'
            sx={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                width: '100%',
                height: `100%`,
                px: isMobileOrTablet ? "0px" : "48px",
                overflowY: 'auto',
                paddingRight: scrollbarWidth > 0 ? `${scrollbarWidth}px` : 0,
                pt: isMobileOrTablet ? "40px" : "0px",
                pb: isMobileOrTablet ? "84px" : "0px",
                transition: "padding-top 0.3s ease",
            }}
        >
            <Box
                className="presentation-wrapper"
                sx={{
                    width: {
                        xs: '90%',
                        sm: '90%',
                        md: '70%',
                        lg: '70%',
                    },
                    maxWidth: '800px',
                }}
            >
                <Box id="chat-history">
                    {initialConversationMessages.map((msg, index) => (
                        <Box className="messages-wrapper" key={msg.id}
                            sx={{
                                mb: index === initialConversationMessages.length - 1 ? 4 : 1,
                                width: '100%'
                            }}
                        >
                            {renderMessageContent(msg, index)}
                            {isMobileOrTablet && openRightSection && activeInfoMessageIdForPanel === msg.id && (
                                <Box sx={{ mt: 2 }}>
                                    {renderRightSidePanel()}
                                </Box>
                            )}
                        </Box>
                    ))}
                </Box>
                {!window.location.href.includes('impact-ai-prod.app') && (
                    <Box className="prompt-wrapper">
                        <PromptSection
                            centeredContent={false}
                            handleChange={handleUserPrompt}
                            queryLabelText={PROMPT_LABEL}
                            isLoading={displaySystemLoader || loadingMessages}
                            dataTags={{}}
                            selectedTag={''}
                            onCloseDropdown={() => { }}
                            chipClickCount={() => { }}
                        />
                    </Box>
                )}
            </Box>
        </Box>
    );

    const renderRightSidePanel = () => {
        if (openRightSection === "sources") {
            return (
                <Sources
                    key={`sources-panel-${activeInfoMessageIdForPanel}`}
                    onClose={handleCloseRightSection}
                    sources={panelSources}
                    messageId={activeInfoMessageIdForPanel}
                    selectedStudy={selectedStudy}
                    activeSourcePaperIds={activeSourcePaperIds}
                    activeSourceMessageId={activeSourceMessageId}
                    displayCloseButton={true}
                    onClearFilter={() => {
                        setActiveSourcePaperIds([]);
                        setActiveSourceMessageId(null);
                    }}
                />
            );
        } else if (openRightSection === "charts") {
            return (
                <ComparisonView
                    key={`plot-section-right-${activeInfoMessageIdForPanel}`}
                    informationId={activeInfoMessageIdForPanel}
                    sources={panelSources}
                    theme={theme}
                    onClose={handleCloseRightSection}
                    activePlotDetails={activePlotDetails}
                    activeSourcePaperIds={activeSourcePaperIds}
                    activeSourceMessageId={activeSourceMessageId}
                    selectedStudy={selectedStudy}
                    onSelectStudy={setSelectedStudy}
                />
            );
        }
        return null;
    };

    const renderTwoColumnLayout = useCallback(() => {
        const rightPanelOpenOffset = '40px';
        const gapBetweenColumns = '2vw';

        let leftColumnCalculatedWidth;
        let stickyPanelCalculatedWidth;
        if (openRightSection) {
            if (openRightSection === 'charts') {
                if (isSidebarCollapsed) {
                    leftColumnCalculatedWidth = `45vw`;
                    stickyPanelCalculatedWidth = `45vw`;
                } else {
                    leftColumnCalculatedWidth = `45vw`;
                    stickyPanelCalculatedWidth = `35vw`;
                }
            } else if (openRightSection === 'sources') {
                if (isSidebarCollapsed) {
                    leftColumnCalculatedWidth = `45vw`;
                    stickyPanelCalculatedWidth = `42vw`;
                } else {
                    leftColumnCalculatedWidth = `45vw`;
                    stickyPanelCalculatedWidth = `35vw`;
                }
            } else {
                leftColumnCalculatedWidth = '45vw';
                stickyPanelCalculatedWidth = '35vw';
            }
        } else {
            leftColumnCalculatedWidth = '45vw';
            stickyPanelCalculatedWidth = '35vw';
        }

        return (
            <Box
                sx={{
                    display: 'flex',
                    width: '100%',
                    height: 'calc(100vh - 104px)',
                    overflow: 'hidden',
                    position: 'relative',
                    justifyContent: 'center',
                    alignItems: 'flex-start',
                    maxWidth: '100%',
                }}
            >
                <Box
                    ref={conversationWrapperScrollRef}
                    className='conversation-wrapper'
                    sx={{
                        display: 'flex',
                        width: '100%',
                        maxWidth: '100%',
                        height: `calc(100% - 104px)`,
                        overflowY: openRightSection ? 'hidden' : 'auto',
                        paddingRight: openRightSection ? 0 : (scrollbarWidth > 0 ? `${scrollbarWidth}px` : 0),
                        px: "0px",
                        pt: "0px",
                        pb: "0px",
                        overflowX: 'hidden',
                        transition: 'width 0.3s ease-in-out, max-width 0.3s ease-in-out, flex 0.3s ease-in-out',
                        justifyContent: 'center',
                        alignItems: 'flex-start',
                        flexShrink: 0,
                        scrollbarGutter: 'stable',
                    }}
                >
                    <Box
                        ref={leftColumnScrollRef}
                        id='left-column'
                        flexGrow={0}
                        flexShrink={0}
                        width={leftColumnCalculatedWidth}
                        minWidth={'350px'}
                        className='conversation-wrapper-inner'
                        sx={{
                            transition: 'width 0.3s ease-in-out, margin 0.3s ease-in-out',
                            height: '100%',
                            overflowY: openRightSection ? 'auto' : 'visible',
                            paddingRight: openRightSection && scrollbarWidth > 0 ? `${scrollbarWidth}px` : 0,
                            px: (isMobileOrTablet
                                ? "0px"
                                : {
                                    xs: "0px", sm: "0px", md: "40px", lg: "40px", xl: "40px", xxl: "40px", uhd: "40px", '4k': "40px"
                                }),
                            ml: openRightSection ? 0 : 'auto',
                            mr: 'auto',
                            scrollbarGutter: 'stable',
                        }}
                    >
                        <Grid
                            container
                            direction="column"
                            className='messages-wrapper'
                        >
                            <Box id="chat-history" px={3}>
                                {initialConversationMessages.map((msg, index) => (
                                    <Grid key={msg.id} item xs={12}
                                        sx={{
                                            mb: index === initialConversationMessages.length - 1 ? 4 : 1
                                        }}
                                    >
                                        {renderMessageContent(msg, index)}
                                    </Grid>
                                ))}
                            </Box>
                            {!window.location.href.includes('impact-ai-prod.app') && (
                                <Grid item xs={12} className="prompt-wrapper">
                                    <PromptSection
                                        centeredContent={false}
                                        handleChange={handleUserPrompt}
                                        queryLabelText={PROMPT_LABEL}
                                        isLoading={displaySystemLoader || loadingMessages}
                                        dataTags={{}}
                                        selectedTag={''}
                                        onCloseDropdown={() => { }}
                                        chipClickCount={() => { }}
                                    />
                                </Grid>
                            )}
                        </Grid>
                    </Box>
                </Box>
                <Box
                    className="sticky-panel"
                    sx={{
                        width: stickyPanelCalculatedWidth,
                        maxWidth: stickyPanelCalculatedWidth,
                        minWidth: openRightSection ? { xs: '0px', md: '250px' } : '0px',
                        transition: 'width 0.3s ease-in-out, opacity 0.3s ease-in-out, max-width 0.3s ease-in-out, min-width 0.3s ease-in-out, visibility 0.3s ease-in-out, right 0.3s ease-in-out',
                        position: 'fixed',
                        top: '82px',
                        right: openRightSection
                            ? rightPanelOpenOffset
                            : `calc(-${stickyPanelCalculatedWidth} - ${gapBetweenColumns})`,
                        height: 'calc(100vh - 120px)',
                        overflowY: 'auto',
                        visibility: openRightSection ? 'visible' : 'hidden',
                        opacity: openRightSection ? 1 : 0,
                        flexShrink: 0,
                        flexGrow: 0,
                        zIndex: 100,
                        scrollbarGutter: 'stable',
                    }}
                >
                    {openRightSection && renderRightSidePanel()}
                </Box>
            </Box>
        );
    }, [
        openRightSection,
        isSidebarCollapsed,
        scrollbarWidth,
        isMobileOrTablet,
        initialConversationMessages,
        renderMessageContent,
        handleUserPrompt,
        displaySystemLoader,
        loadingMessages,
        renderRightSidePanel,
        conversationWrapperScrollRef,
        leftColumnScrollRef,
    ]);

    if (initialConversationMessages.length === 0 || loadingMessages) {
        return (
            <Box
                display="flex"
                flexDirection="column"
                width={isMobileOrTablet ? "90%" : "848px"}
                mx="auto"
                className="chat-conversation"
                sx={{
                    height: 'calc(100vh - 160px)',
                    overflowY: "hidden",
                    transition: "padding-top 0.3s ease",
                }}
            >
                <Box
                    className="chat-history-container"
                    display="flex"
                    flexDirection="column"
                    flexGrow={1}
                    px={0}
                    height="100%"
                    justifyContent="center"
                    alignItems="center"
                    position="relative"
                >
                    <CenteredPageLoader isMobile={isMobile} />
                </Box>
            </Box>
        );
    }

    return (
        <Box
            ref={containerRef}
            className="chat-conversation"
            sx={{
                display: 'flex',
                flexDirection: 'column',
                width: '100%',
                mx: 'auto',
                height: window.location.href.includes('impact-ai-prod.app') ? '100vh' : 'calc(100vh - 82px)',
                flexGrow: 1,
            }}
        >
            {isMobileOrTablet ? renderSingleColumnLayout() : renderTwoColumnLayout()}
        </Box>
    );
};

export default DisplayConversation;