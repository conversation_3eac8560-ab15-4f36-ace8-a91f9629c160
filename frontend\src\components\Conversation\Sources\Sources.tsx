import React, { useEffect, useState, useCallback, useRef } from "react";
import {
  Box,
  Card,
  CardContent,
  Typography,
  Chip,
  Collapse,
  IconButton,
  Link,
  useTheme,
  alpha,
} from "@mui/material";
import { Source } from "../../../types/ConversationTypes";
import ArticleOutlinedIcon from "@mui/icons-material/ArticleOutlined";
import OpenInNewIcon from '@mui/icons-material/OpenInNew';
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import ExpandLessIcon from "@mui/icons-material/ExpandLess";
import CloseIcon from '@mui/icons-material/Close';
import { AnimatePresence, motion } from "framer-motion";
import { sanitizeSourceText } from "../../../utils/sourceUtils";
import { invertName, toTitleCase, sentenceCase } from "../../../utils/text";

interface SourcesProps {
  onClose: () => void;
  sources: Source[] | undefined | null;
  messageId: string | null;
  selectedStudy: string;
  activeSourcePaperIds: string[];
  activeSourceMessageId: string | null;
  displayCloseButton?: boolean;
  onClearFilter?: () => void;
  hideSourceCount?: boolean;
  /** Whether to show expand/collapse UI. */
  expandable?: boolean;
  /** Whether to always show expanded content without toggle arrows. */
  alwaysExpanded?: boolean;
  /** Whether to hide the article icon on the left side of the card. */
  hideArticleIcon?: boolean;
  /** Maximum number of lines to show for abstract text before truncating. Default is 3. */
  maxAbstractLines?: number;
  /** Disable the blue border around the selected source card. */
  disableSelectedBorder?: boolean;
  /** Disable auto-scrolling to the selected source card. */
  disableAutoScroll?: boolean;
  /** Disable highlighting the selected source card. */
  disableSelectedHighlight?: boolean;
  /** Spacing between icon and content in pixels. Default is 1 (theme spacing unit). */
  iconSpacing?: number;
  /** Show close button on individual source cards. */
  showCloseButton?: boolean;
  /** Callback when close button on source card is clicked. */
  onCloseSource?: (sourceId: string) => void;
  /** Override intervention details for display. */
  interventionDetails?: {
    name: string;
    description: string;
  };
  /** Override outcome details for display. */
  outcomeDetails?: {
    name: string;
    description: string;
  };
}

const Sources: React.FC<SourcesProps> = ({
  onClose,
  sources,
  messageId,
  selectedStudy,
  activeSourcePaperIds,
  activeSourceMessageId,
  displayCloseButton = true,
  onClearFilter,
  hideSourceCount = false,
  expandable = false,
  alwaysExpanded = false,
  hideArticleIcon = false,
  maxAbstractLines = 3,
  disableSelectedBorder = false,
  disableAutoScroll = false,
  disableSelectedHighlight = false,
  iconSpacing = 1,
  showCloseButton = false,
  onCloseSource,
  interventionDetails,
  outcomeDetails,
}) => {
  const [currentSources, setCurrentSources] = useState<Source[]>([]);
  const [, setLoading] = useState(true);
  const [, setError] = useState<string | null>(null);
  const [expandedSources, setExpandedSources] = useState<Set<string>>(new Set());
  const isFiltered = messageId === activeSourceMessageId && activeSourcePaperIds.length > 0;
  const theme = useTheme();
  const cardRefs = useRef<(HTMLDivElement | null)[]>([]);
  
  const ICON_AREA_WIDTH = '80px';

  const toggleSourceExpansion = useCallback((sourceId: string) => {
    if (!expandable) return;
    setExpandedSources(prev => {
      const newSet = new Set(prev);
      if (newSet.has(sourceId)) {
        newSet.delete(sourceId);
      } else {
        newSet.add(sourceId);
      }
      return newSet;
    });
  }, [expandable]);

  const isSourceExpanded = useCallback((sourceId: string) => {
    return expandedSources.has(sourceId);
  }, [expandedSources]);

  const selectedCardStyle = disableSelectedBorder ? {} : {
    borderColor: theme.palette.primary.light,
    borderWidth: 1,
  };
  const handleShowAll = () => {
    onClearFilter && onClearFilter();
  };


  useEffect(() => {
    if (messageId) {
      if (sources) {
        let validSources = sources.filter(
          (item) =>
            (typeof item.citation === 'string' && item.citation.trim()) ||
            (typeof item.title === 'string' && item.title.trim())
        ).sort((b, a) => {
          if (a.position && b.position) {
            return b.position - a.position;
          }
          return 1;
        });

        if (isFiltered && activeSourcePaperIds.length > 0) {
          validSources = validSources.filter(source =>
            source.paper_id && activeSourcePaperIds.includes(source.short_paper_id)
          );
        }

        setCurrentSources(validSources);
        setLoading(false);
        setError(validSources.length === 0 ? "No valid sources available for this message." : null);

        if (!disableAutoScroll && !hideSourceCount && selectedStudy) {
          const selectedIndex = validSources.findIndex(
            (item) => String(item.short_paper_id) === String(selectedStudy)
          );
          if (selectedIndex !== -1) {
            setTimeout(() => {
              if (cardRefs.current[selectedIndex]) {
                cardRefs.current[selectedIndex]?.scrollIntoView({
                  behavior: "smooth",
                  block: "center",
                });
              }
            }, 0);
          }
        }
      } else if (sources === null) {
        setLoading(false);
        setError('No sources available for this message.');
      } else {
        setLoading(true);
        setError(null);
      }
    } else {
      setCurrentSources([]);
      setLoading(false);
      setError(null);
    }
  }, [messageId, sources, selectedStudy, isFiltered, activeSourcePaperIds, disableAutoScroll, hideSourceCount]);


  const generateLink = useCallback((url: string | undefined): string | undefined => {
    if (!url) return undefined;
    if (url.startsWith('http')) {
      return url;
    } else {
      return `https://doi.org/${url}`;
    }
  }, []);

  const setCardRef = useCallback((el: HTMLDivElement | null, index: number) => {
    if (el) {
      cardRefs.current[index] = el;
    }
  }, []);


  // Using sentenceCase from utils/text.ts

  const formatAuthorsMLA = (authorsRaw: string, maxLength = 80) => {
    if (!authorsRaw) return '';
    const sanitized = authorsRaw.replace(/\s*\[ORCID:[^\]]+\]/g, '');
    const authors = sanitized.split(';').map(a => a.trim()).filter(Boolean);
    if (authors.length === 0) return '';
    let result = '';
    if (authors.length === 1) {
      result = invertName(authors[0]);
    } else if (authors.length === 2) {
      result = `${invertName(authors[0])}, and ${authors[1]}`;
    } else {
      result = `${invertName(authors[0])}, et al.`;
    }
    if (result.length > maxLength) {
      let truncated = result.slice(0, maxLength - 1);
      const lastSpace = truncated.lastIndexOf(' ');
      if (lastSpace > 0) truncated = truncated.slice(0, lastSpace);
      result = truncated + '…';
    }
    return result;
  };

  const handleClearFilter = () => {
    setCurrentSources(sources);

    const sourceListScroll = document.getElementById('source-list-scroll');
    if (sourceListScroll) {
      sourceListScroll.scrollTop = 0;
    }

    setSelectedStudy(null);
  };

  return (
    <>
      {currentSources && currentSources.length > 0 ? (
        hideSourceCount ? (
          <AnimatePresence initial={false}>
            {currentSources.map((item, index) => {
              const isSelected = selectedStudy === item.short_paper_id;
              const isExpanded = isSourceExpanded(item.id);
              return (
                <motion.div
                  key={index}
                  style={{
                    overflow: 'hidden',
                    marginBottom: theme.spacing(1.5),
                  }}
                  ref={(el) => setCardRef(el, index)}
                >                      <Card
                        variant="outlined"
                        sx={{
                          border: `1px solid ${theme.palette.divider}`,
                          bgcolor: (theme as any).common?.white?.main || '#fff',
                          ...(isSelected && !disableSelectedHighlight ? selectedCardStyle : {}),
                        }}
                      >
                    <CardContent
                      sx={{
                        position: 'relative',
                        padding: '12px !important',
                        '&:last-child': { paddingBottom: '12px !important' },
                      }}
                    >
                      <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: iconSpacing }}>
                        {!hideArticleIcon && (
                          <Box sx={{ flex: '0 0 auto', width: '24px', display: 'flex', alignItems: 'flex-start', justifyContent: 'center', mt: '2px' }}>
                            <ArticleOutlinedIcon sx={{ width: 20, height: 20, color: theme.palette.text.secondary }} />
                          </Box>
                        )}
                        <Box sx={{ flex: '1 1 auto', overflow: 'hidden', maxWidth: '100%' }}>
                          {item?.title && (
                            <Typography
                              variant="body2"
                              sx={{
                                fontFamily: 'Roboto, sans-serif',
                                fontWeight: 500,
                                color: theme.palette.text.primary,
                                fontSize: '14px',
                                lineHeight: '1.4',
                                letterSpacing: '0.14px',
                                mb: 0.5,
                                textAlign: 'left',
                                alignItems: 'center',
                                display: 'flex',
                              }}
                            >
                              {typeof item.title === 'string' ? toTitleCase(sanitizeSourceText(item.title)) : item.title}
                            </Typography>
                          )}
                          
                          {(() => {
                            let authorsRaw = item.authors;
                            let year = '';
                            if ((!authorsRaw || !authorsRaw.trim()) && item.citation) {
                              // Clean the citation before parsing
                              const cleanCitation = sanitizeSourceText(item.citation);
                              let authorMatch = cleanCitation.match(/^(.+?)(?=\s*\(\d{4}\))/);
                              if (authorMatch) {
                                authorsRaw = authorMatch[1].trim();
                              } else {
                                const altMatch = cleanCitation.match(/^([^.;]+(?:;[^.;]+)*)(?=\.|;|$)/);
                                if (altMatch) authorsRaw = altMatch[1].trim();
                              }
                            } else if (authorsRaw) {
                              // Clean the authors string
                              authorsRaw = sanitizeSourceText(authorsRaw);
                            }
                            
                            if (item.citation) {
                              const cleanCitation = sanitizeSourceText(item.citation);
                              const yearMatch = cleanCitation.match(/\((\d{4})\)/);
                              if (yearMatch) {
                                year = yearMatch[1];
                              }
                            }
                            let authorLine = '';
                            let fullAuthors = '';
                            if (authorsRaw) {
                              authorsRaw = authorsRaw.replace(/^\(?\d{4}\)?[.,;:\s-]*/g, '').replace(/[.,;:\s-]*\(?\d{4}\)?$/g, '');
                              const authorsNoOrcid = authorsRaw.replace(/\s*\[ORCID:[^\]]+\]/g, '');
                              authorLine = formatAuthorsMLA(authorsNoOrcid);
                              fullAuthors = authorsNoOrcid.split(';')
                                .map(author => {
                                  const trimmed = author.trim();
                                      if (trimmed.includes(',')) return trimmed;
                                  return invertName(trimmed);
                                })
                                .filter(Boolean)
                                .join(', ');
                            }
                            let showYear = year;
                            if (item.citation && item.citation.trim().startsWith(`(${year})`)) {
                              showYear = '';
                            }
                            if (authorLine || year) {
                              return (
                                <Typography
                                  variant="body2"
                                  sx={{
                                    fontFamily: 'Roboto, sans-serif',
                                    fontWeight: 400,
                                    color: theme.palette.text.primary,
                                    fontSize: '14px',
                                    lineHeight: '1.66',
                                    letterSpacing: '0.4px',
                                    mb: 0.5,
                                    textAlign: 'left',
                                    alignItems: 'center',
                                    display: 'flex',
                                    maxWidth: '100%',
                                    overflow: 'hidden',
                                    textOverflow: 'ellipsis',
                                    whiteSpace: 'nowrap',
                                  }}
                                  title={fullAuthors + (showYear ? ` (${showYear})` : '')}
                                >
                                  {authorLine}{showYear ? ` (${showYear})` : ''}
                                </Typography>
                              );
                            }
                            return null;
                          })()}
                          {item?.journal_name && (
                            <Typography
                              variant="body2"
                              sx={{
                                fontFamily: 'Roboto, sans-serif',
                                fontStyle: 'italic',
                                fontWeight: 400,
                                color: theme.palette.text.primary,
                                fontSize: '14px',
                                lineHeight: '1.66',
                                letterSpacing: '0.4px',
                                textAlign: 'left',
                                display: 'block',
                                maxWidth: '100%',
                                overflow: 'hidden',
                                textOverflow: 'ellipsis',
                                whiteSpace: 'nowrap',
                              }}
                              title={sanitizeSourceText(item.journal_name)}
                            >
                              {sanitizeSourceText(item.journal_name)}
                            </Typography>
                          )}
                          {(item?.volume || item?.issue || item?.pages) && (
                            <Typography
                              variant="body2"
                              sx={{
                                fontFamily: 'Roboto, sans-serif',
                                fontWeight: 400,
                                color: theme.palette.text.primary,
                                fontSize: '14px',
                                lineHeight: '1.66',
                                letterSpacing: '0.4px',
                                textAlign: 'left',
                                display: 'block',
                                maxWidth: '100%',
                                overflow: 'hidden',
                                textOverflow: 'ellipsis',
                                whiteSpace: 'nowrap',
                              }}
                              title={[
                                item.volume ? `Vol. ${item.volume}` : null,
                                item.issue ? `No. ${item.issue}` : null,
                                item.pages ? `pp. ${item.pages}` : null
                              ].filter(Boolean).join(', ')}
                            >
                              {[
                                item.volume ? `Vol. ${item.volume}` : null,
                                item.issue ? `No. ${item.issue}` : null,
                                item.pages ? `pp. ${item.pages}` : null
                              ].filter(Boolean).join(', ')}
                            </Typography>
                          )}
                        </Box>
                        <Box sx={{ flex: '0 0 auto', display: 'flex', alignItems: 'flex-start', gap: 2, mt: '2px' }}>
                          {item?.doi_url && (
                            <IconButton
                              component={Link}
                              href={generateLink(item.doi_url || undefined)}
                              target="_blank"
                              rel="noopener noreferrer"
                              size="small"
                              sx={{
                                color: theme.palette.text.secondary,
                                padding: '6px', // increase padding for larger hover circle
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                width: '36px', // increase width/height for larger hover area
                                height: '36px',
                                borderRadius: '50%',
                                transition: 'background 0.2s',
                                '&:hover': {
                                  background: theme.palette.action.hover,
                                },
                              }}
                              onClick={(e) => e.stopPropagation()}
                            >
                              <OpenInNewIcon sx={{ width: 20, height: 20 }} />
                            </IconButton>
                          )}
                          {showCloseButton && (
                            <IconButton
                              size="small"
                              sx={{ 
                                color: theme.palette.text.secondary,
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                padding: '4px'
                              }}
                              onClick={(e) => {
                                e.stopPropagation();
                                if (onCloseSource && item.short_paper_id) {
                                  onCloseSource(item.short_paper_id);
                                }
                              }}
                            >
                              <CloseIcon sx={{ width: 18, height: 18 }} />
                            </IconButton>
                          )}
                        </Box>
                      </Box>

                      {/* Abstract Section */}
                      {item?.abstract && (
                        <Box sx={{ mb: 1, ml: hideArticleIcon ? 0 : '32px', pr: ICON_AREA_WIDTH }}>
                          <Typography
                            variant="body2"
                            sx={{
                              fontFamily: 'Roboto, sans-serif',
                              fontWeight: 400,
                              color: theme.palette.text.secondary,
                              fontSize: '14px',
                              lineHeight: '1.66',
                              letterSpacing: '0.4px',
                              textAlign: 'left',
                              alignItems: 'center',
                              display: '-webkit-box',
                              WebkitLineClamp: maxAbstractLines,
                              WebkitBoxOrient: 'vertical',
                              overflow: 'hidden',
                              textOverflow: 'ellipsis',
                            }}
                          >
                            {sanitizeSourceText(item.abstract)}
                          </Typography>
                        </Box>
                      )}

                      {/* Tags Row */}
                      <Box sx={{ 
                        display: 'flex', 
                        flexWrap: 'wrap', 
                        gap: 0.5, 
                        mb: 0.25, 
                        ml: hideArticleIcon ? 0 : '32px', 
                        pr: ICON_AREA_WIDTH
                      }}>
                          {(() => {
                          const capitalizeWords = (str: string) => {
                            if (!str) return str;
                            const lowerConjunctions = ['and', 'of'];
                            return str.split(/\s+/).map((word, idx) => {
                              if (lowerConjunctions.includes(word.toLowerCase()) && idx !== 0) {
                                return word.toLowerCase();
                              }
                              return word.charAt(0).toUpperCase() + word.slice(1).toLowerCase();
                            }).join(' ');
                          };
                          let chips = [];
                          if (item?.country) {
                            chips.push(
                              <Chip
                                key="country"
                                label={`Country: ${capitalizeWords(item.country)}`}
                                size="small"
                                variant="outlined"
                                sx={{
                                  fontFamily: 'Roboto, sans-serif',
                                  fontWeight: 500,
                                  fontSize: '13px',
                                  lineHeight: '14px',
                                  letterSpacing: 0,
                                  height: '22px',
                                  borderRadius: '4px',
                                  borderWidth: '1px',
                                  borderColor: '#D4E4FC',
                                  color: theme.palette.text.secondary,
                                  px: '4px',
                                  py: '0px',
                                  textAlign: 'left',
                                  alignItems: 'center',
                                  display: 'flex',
                                }}
                              />
                            );
                          }
                          // Temporarily hidden - Quality Score chip
                          // if (item?.quality_score_category) {
                          //   chips.push(
                          //     <Chip
                          //       key="quality"
                          //       label={`Quality Score: ${capitalizeWords(item.quality_score_category)}`}
                          //       size="small"
                          //       variant="outlined"
                          //       sx={{
                          //         fontFamily: 'Roboto, sans-serif',
                          //         fontWeight: 500,
                          //         fontSize: '13px',
                          //         lineHeight: '14px',
                          //         letterSpacing: 0,
                          //         height: '22px',
                          //         borderRadius: '4px',
                          //         borderWidth: '1px',
                          //         borderColor: '#D4E4FC',
                          //         color: theme.palette.text.secondary,
                          //         px: '4px',
                          //         py: '0px',
                          //         textAlign: 'left',
                          //         alignItems: 'center',
                          //         display: 'flex',
                          //       }}
                          //     />
                          //   );
                          // }
                          if (item?.sector) {
                            let sectors = item.sector.split(';').map(s => s.trim()).filter(Boolean);
                            let formatted = sectors.map(capitalizeWords).join(', ');
                            chips.push(
                              <Chip
                                key="sector"
                                label={`Sector: ${formatted}`}
                                size="small"
                                variant="outlined"
                                sx={{
                                  fontFamily: 'Roboto, sans-serif',
                                  fontWeight: 500,
                                  fontSize: '13px',
                                  lineHeight: '14px',
                                  letterSpacing: 0,
                                  height: '22px',
                                  borderRadius: '4px',
                                  borderWidth: '1px',
                                  borderColor: '#D4E4FC',
                                  color: theme.palette.text.secondary,
                                  px: '4px',
                                  py: '0px',
                                  textAlign: 'left',
                                  alignItems: 'center',
                                  display: 'flex',
                                }}
                              />
                            );
                          }
                          return chips;
                        })()}
                      </Box>
                      {expandable && !alwaysExpanded && !isExpanded && (item?.intervention_name || item?.outcome_name) && (
                        <Box sx={{ 
                          position: 'absolute', 
                          right: 12, 
                          bottom: 12, 
                          width: '24px', 
                          height: '24px',
                          display: 'flex', 
                          justifyContent: 'center',
                          alignItems: 'center'
                        }}>
                          <IconButton
                            size="small"
                            onClick={() => toggleSourceExpansion(item.id)}
                            sx={{
                              color: theme.palette.text.secondary,
                              m: 0,
                              p: 0,
                              width: '24px',
                              height: '24px',
                              '&:hover': {
                                background: 'transparent',
                              }
                            }}
                          >
                            <ExpandMoreIcon sx={{ width: 20, height: 20 }} />
                          </IconButton>
                        </Box>
                      )}

                      {/* Expandable Content */}
                      {((expandable && isExpanded) || alwaysExpanded) && (item?.intervention_name || item?.outcome_name) && (
                        <Box sx={{ 
                          mt: 1, 
                          pt: 1,
                          position: 'relative',
                          ml: hideArticleIcon ? 0 : '32px', 
                          mb: 0, 
                          pb: alwaysExpanded ? 1 : 3,
                          pr: ICON_AREA_WIDTH
                        }}>
                          <Box 
                            sx={{
                              position: 'absolute',
                              top: 0,
                              left: 0,
                              right: ICON_AREA_WIDTH,
                              height: '1px',
                              backgroundColor: theme.palette.divider
                            }}
                          />
                          
                          {expandable && !alwaysExpanded && (
                            <Box sx={{ 
                              position: 'absolute', 
                              right: 12, 
                              bottom: 12, 
                              width: '24px', 
                              height: '24px',
                              display: 'flex', 
                              justifyContent: 'center',
                              alignItems: 'center',
                              zIndex: 2
                            }}>
                              <IconButton
                                size="small"
                                onClick={() => toggleSourceExpansion(item.id)}
                                sx={{
                                  color: theme.palette.text.secondary,
                                  m: 0,
                                  p: 0,
                                  width: '24px',
                                  height: '24px',
                                  '&:hover': {
                                    background: 'transparent',
                                  }
                                }}
                              >
                                <ExpandLessIcon sx={{ width: 20, height: 20 }} />
                              </IconButton>
                            </Box>
                          )}
                          
                          <Box sx={{ 
                            position: 'relative', 
                            zIndex: 1,
                            backgroundColor: (theme as any).common?.white?.main || theme.palette.background.paper
                          }}>
                          {(interventionDetails || item?.intervention_name) && (
                            <Box sx={{ mb: (outcomeDetails || item?.outcome_name) ? 2 : 0 }}>
                              <Typography
                                variant="body2"
                                sx={{
                                  fontFamily: 'Roboto, sans-serif',
                                  fontWeight: 500,
                                  color: theme.palette.text.primary,
                                  fontSize: '14px',
                                  lineHeight: '1.4',
                                  letterSpacing: '0.14px',
                                  mb: 0.5,
                                }}
                              >
                                Intervention: {interventionDetails?.name || (item.intervention_name ? sentenceCase(item.intervention_name) : 'Unknown Intervention')}
                              </Typography>
                              <Typography
                                variant="body2"
                                sx={{
                                  fontFamily: 'Roboto, sans-serif',
                                  fontWeight: 400,
                                  color: theme.palette.text.primary,
                                  fontSize: '14px',
                                  lineHeight: '1.66',
                                  letterSpacing: '0.4px',
                                  ml: 0,
                                }}
                              >
                                Description: {interventionDetails?.description || sentenceCase(item?.intervention_details || 'No description available')}
                              </Typography>
                            </Box>
                          )}

                          {(outcomeDetails || item?.outcome_name) && (
                            <Box sx={{ mb: 0 }}>
                              <Typography
                                variant="body2"
                                sx={{
                                  fontFamily: 'Roboto, sans-serif',
                                  fontWeight: 500,
                                  color: theme.palette.text.primary,
                                  fontSize: '14px',
                                  lineHeight: '1.4',
                                  letterSpacing: '0.14px',
                                  mb: 0.5,
                                }}
                              >
                                Outcome: {outcomeDetails?.name || (item.outcome_name ? sentenceCase(item.outcome_name) : 'Unknown Outcome')}
                              </Typography>                                        <Typography
                                          variant="body2"
                                          sx={{
                                            fontFamily: 'Roboto, sans-serif',
                                            fontWeight: 400,
                                            color: theme.palette.text.primary,
                                            fontSize: '14px',
                                            lineHeight: '1.66',
                                            letterSpacing: '0.4px',
                                            ml: 0,
                                          }}
                                        >
                                          Description: {outcomeDetails?.description || sentenceCase(item?.outcome_details || 'No description available')}
                                        </Typography>
                            </Box>
                          )}
                        </Box>
                        </Box>
                      )}
                    </CardContent>
                  </Card>
                </motion.div>
              );
            })}
          </AnimatePresence>
        ) : (
          <Card
            elevation={0}
            sx={{
              width: '100%',
              border: `1px solid ${theme.palette.divider}`,
              borderRadius: '8px',
              display: 'flex',
              flexDirection: 'column',
              maxHeight: "100%",
              background: theme.palette.common.white,
            }}
          >
            <Box
              sx={{
                p: 1.5,
                background: theme.palette.common.white,
                zIndex: 1,
                flexShrink: 0,
              }}
            >
              <Box
                sx={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  mb: 0.5,
                }}
              >
                {(() => {
                  if (hideSourceCount) return null;
                  const totalCount = sources?.filter(
                    (item) =>
                      (typeof item.citation === 'string' && item.citation.trim()) ||
                      (typeof item.title === 'string' && item.title.trim())
                  ).length || 0;
                  const filteredCount = currentSources.length;
                  if (isFiltered) {
                    return (
                      <Box
                        sx={{
                          display: 'flex',
                          alignItems: 'center',
                          borderRadius: '100px',
                          background: theme.palette.common.white,
                          border: `1px solid ${theme.palette.secondary.main}`,
                          pl: 2,
                          pr: 1,
                          height: '24px',
                          color: theme.palette.primary.main,
                          boxShadow: 'none',
                          gap: 0.5,
                          width: 'max-content',
                          maxWidth: { xs: '100%', md: '200px' },
                        }}
                      >
                        <Box 
                          component="span" 
                          sx={{ 
                            display: 'flex', 
                            alignItems: 'center', 
                            height: '100%',
                            fontFamily: 'Roboto, sans-serif',
                            fontSize: '13px',
                            fontWeight: 400,
                            lineHeight: '18px',
                            letterSpacing: '0.16px',
                          }}
                        >
                          {`${filteredCount} of ${totalCount} sources`}
                        </Box>
                        <IconButton
                          size="small"
                          onClick={handleShowAll}
                          sx={{ 
                            ml: 0, 
                            p: 0, 
                            color: alpha(theme.palette.primary.main, 0.7), // 70% opacity using alpha function
                            height: '16px',
                            width: '16px',
                            minWidth: '16px',
                            minHeight: '16px',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center'
                          }}
                          aria-label="Clear filter"
                        >
                          <CloseIcon sx={{ fontSize: '16px' }} />
                        </IconButton>
                      </Box>
                    );
                  } else if (totalCount > 0) {
                    return (
                      <Typography
                        variant="body2"
                        sx={{
                          fontSize: 13,
                          fontWeight: 400,
                          color: theme.palette.text.secondary,
                        }}
                      >
                        {`${totalCount} sources`}
                      </Typography>
                    );
                  }
                  return null;
                })()}
                {displayCloseButton && (
                  <IconButton
                    id='test-kush'
                    onClick={onClose}
                    size="small"
                    sx={{
                      color: theme.palette.text.secondary,
                      ml: 3,
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      padding: '4px'
                    }}
                  >
                    <CloseIcon sx={{ width: 18, height: 18 }} />
                  </IconButton>
                )}
              </Box>
            </Box>
            <Box
              id="source-list-scroll"
              sx={{
                overflowY: 'auto',
                flexGrow: 1,
                p: 1.5,
                pt: 1,
              }}
            >
              <AnimatePresence initial={false}>
                {currentSources.map((item, index) => {
                  const isSelected = selectedStudy === item.short_paper_id;
                  const isExpanded = isSourceExpanded(item.id);
                  return (
                    <motion.div
                      key={index}
                      style={{
                        overflow: 'hidden',
                        marginBottom: theme.spacing(1.5),
                      }}
                      ref={(el) => setCardRef(el, index)}
                    >
                      <Card
                        variant="outlined"
                        sx={{
                          border: `1px solid ${theme.palette.divider}`,
                          background: theme.palette.background.default,
                          ...(isSelected ? selectedCardStyle : {}),
                        }}
                      >
                        <CardContent
                          sx={{
                            position: 'relative',
                            padding: '12px !important',
                            '&:last-child': { paddingBottom: '12px !important' },
                            bgcolor: (theme as any).common?.white?.main || '#fff',
                          }}
                        >
                          <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: iconSpacing }}>
                            {!hideArticleIcon && (
                              <Box sx={{ flex: '0 0 auto', width: '24px', display: 'flex', alignItems: 'flex-start', justifyContent: 'center', mt: '2px' }}>
                                <ArticleOutlinedIcon sx={{ width: 20, height: 20, color: theme.palette.text.secondary }} />
                              </Box>
                            )}
                            <Box sx={{ flex: '1 1 auto', overflow: 'hidden', maxWidth: '100%' }}>
                              {item?.title && (
                                <Typography
                                  variant="body2"
                                  sx={{
                                    fontFamily: 'Roboto, sans-serif',
                                    fontWeight: 500,
                                    color: theme.palette.text.primary,
                                    fontSize: '14px',
                                    lineHeight: '1.4',
                                    letterSpacing: '0.14px',
                                    mb: 0.5,
                                    textAlign: 'left',
                                    alignItems: 'center',
                                    display: 'flex',
                                  }}
                                >
                                  {typeof item.title === 'string' ? toTitleCase(sanitizeSourceText(item.title)) : item.title}
                                </Typography>
                              )}
                              
                              {(() => {
                                let authorsRaw = item.authors;
                                let year = '';
                                if ((!authorsRaw || !authorsRaw.trim()) && item.citation) {
                                  // Clean the citation before parsing
                                  const cleanCitation = sanitizeSourceText(item.citation);
                                  let authorMatch = cleanCitation.match(/^(.+?)(?=\s*\(\d{4}\))/);
                                  if (authorMatch) {
                                    authorsRaw = authorMatch[1].trim();
                                  } else {
                                    const altMatch = cleanCitation.match(/^([^.;]+(?:;[^.;]+)*)(?=\.|;|$)/);
                                    if (altMatch) authorsRaw = altMatch[1].trim();
                                  }
                                } else if (authorsRaw) {
                                  // Clean the authors string
                                  authorsRaw = sanitizeSourceText(authorsRaw);
                                }
                                
                                if (item.citation) {
                                  const cleanCitation = sanitizeSourceText(item.citation);
                                  const yearMatch = cleanCitation.match(/\((\d{4})\)/);
                                  if (yearMatch) {
                                    year = yearMatch[1];
                                  }
                                }
                                let authorLine = '';
                                let fullAuthors = '';
                                if (authorsRaw) {
                                  authorsRaw = authorsRaw.replace(/^\(?\d{4}\)?[.,;:\s-]*/g, '').replace(/[.,;:\s-]*\(?\d{4}\)?$/g, '');
                                  const authorsNoOrcid = authorsRaw.replace(/\s*\[ORCID:[^\]]+\]/g, '');
                                  authorLine = formatAuthorsMLA(authorsNoOrcid);
                                  fullAuthors = authorsNoOrcid.split(';')
                                    .map(author => {
                                      const trimmed = author.trim();
                                      if (trimmed.includes(',')) return trimmed;
                                      return invertName(trimmed);
                                    })
                                    .filter(Boolean)
                                    .join(', ');
                                }
                                let showYear = year;
                                if (item.citation && item.citation.trim().startsWith(`(${year})`)) {
                                  showYear = '';
                                }
                                if (authorLine || year) {
                                  return (
                                    <Typography
                                      variant="body2"
                                      sx={{
                                        fontFamily: 'Roboto, sans-serif',
                                        fontWeight: 400,
                                        color: theme.palette.text.primary,
                                        fontSize: '14px',
                                        lineHeight: '1.66',
                                        letterSpacing: '0.4px',
                                        mb: 0.5,
                                        textAlign: 'left',
                                        alignItems: 'center',
                                        display: 'flex',
                                        maxWidth: '100%',
                                        overflow: 'hidden',
                                        textOverflow: 'ellipsis',
                                        whiteSpace: 'nowrap',
                                      }}
                                      title={fullAuthors + (showYear ? ` (${showYear})` : '')}
                                    >
                                      {authorLine}{showYear ? ` (${showYear})` : ''}
                                    </Typography>
                                  );
                                }
                                return null;
                              })()}
                              {item?.journal_name && (
                                <Typography
                                  variant="body2"
                                  sx={{
                                    fontFamily: 'Roboto, sans-serif',
                                    fontStyle: 'italic',
                                    fontWeight: 400,
                                    color: theme.palette.text.primary,
                                    fontSize: '14px',
                                    lineHeight: '1.66',
                                    letterSpacing: '0.4px',
                                    textAlign: 'left',
                                    display: 'block',
                                    maxWidth: '100%',
                                    overflow: 'hidden',
                                    textOverflow: 'ellipsis',
                                    whiteSpace: 'nowrap',
                                  }}
                                  title={sanitizeSourceText(item.journal_name)}
                                >
                                  {sanitizeSourceText(item.journal_name)}
                                </Typography>
                              )}
                              {(item?.volume || item?.issue || item?.pages) && (
                                <Typography
                                  variant="body2"
                                  sx={{
                                    fontFamily: 'Roboto, sans-serif',
                                    fontWeight: 400,
                                    color: theme.palette.text.primary,
                                    fontSize: '14px',
                                    lineHeight: '1.66',
                                    letterSpacing: '0.4px',
                                    textAlign: 'left',
                                    display: 'block',
                                    maxWidth: '100%',
                                    overflow: 'hidden',
                                    textOverflow: 'ellipsis',
                                    whiteSpace: 'nowrap',
                                  }}
                                  title={[
                                    item.volume ? `Vol. ${item.volume}` : null,
                                    item.issue ? `No. ${item.issue}` : null,
                                    item.pages ? `pp. ${item.pages}` : null
                                  ].filter(Boolean).join(', ')}
                                >
                                  {[
                                    item.volume ? `Vol. ${item.volume}` : null,
                                    item.issue ? `No. ${item.issue}` : null,
                                    item.pages ? `pp. ${item.pages}` : null
                                  ].filter(Boolean).join(', ')}
                                </Typography>
                              )}
                            </Box>
                            <Box sx={{ flex: '0 0 auto', display: 'flex', alignItems: 'center', gap: 0.5 }}>
                              {item?.doi_url && (
                                <IconButton
                                  component={Link}
                                  href={generateLink(item.doi_url || undefined)}
                                  target="_blank"
                                  rel="noopener noreferrer"
                                  size="small"
                                  sx={{ 
                                    color: theme.palette.text.secondary,
                                    padding: '0px',
                                    display: 'flex',
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                    width: '24px',
                                    height: '24px'
                                  }}
                                  onClick={(e) => e.stopPropagation()}
                                >
                                  <OpenInNewIcon sx={{ width: 20, height: 20 }} />
                                </IconButton>
                              )}
                            </Box>
                          </Box>
                          {item?.abstract && (
                            <Box sx={{ mb: 1, ml: '32px', pr: '40px' }}>
                              <Typography
                                variant="body2"
                                sx={{
                                  fontFamily: 'Roboto, sans-serif',
                                  fontWeight: 400,
                                  color: theme.palette.text.secondary,
                                  fontSize: '14px',
                                  lineHeight: '1.66',
                                  letterSpacing: '0.4px',
                                  textAlign: 'left',
                                  alignItems: 'center',
                                  display: '-webkit-box',
                                  WebkitLineClamp: 3,
                                  WebkitBoxOrient: 'vertical',
                                  overflow: 'hidden',
                                  textOverflow: 'ellipsis',
                                }}
                              >
                                {sanitizeSourceText(item.abstract)}
                              </Typography>
                            </Box>
                          )}
                          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, mb: 0.25, ml: '32px', pr: '40px' }}>
                            {(() => {
                              const capitalizeWords = (str: string) => {
                                if (!str) return str;
                                const lowerConjunctions = ['and', 'of'];
                                return str.split(/\s+/).map((word, idx) => {
                                  if (lowerConjunctions.includes(word.toLowerCase()) && idx !== 0) {
                                    return word.toLowerCase();
                                  }
                                  return word.charAt(0).toUpperCase() + word.slice(1).toLowerCase();
                                }).join(' ');
                              };
                              let chips = [];
                              if (item?.country) {
                                chips.push(
                                  <Chip
                                    key="country"
                                    label={`Country: ${capitalizeWords(item.country)}`}
                                    size="small"
                                    variant="outlined"
                                    sx={{
                                      fontFamily: 'Roboto, sans-serif',
                                      fontWeight: 500,
                                      fontSize: '12px',
                                      lineHeight: '14px',
                                      letterSpacing: 0,
                                      height: '22px',
                                      borderRadius: '4px',
                                      borderWidth: '1px',
                                      borderColor: '#D4E4FC',
                                      color: theme.palette.text.secondary,
                                      px: '4px',
                                      py: '0px',
                                      textAlign: 'left',
                                      alignItems: 'center',
                                      display: 'flex',
                                    }}
                                  />
                                );
                              }
                              // Temporarily hidden - Quality Score chip
                              // if (item?.quality_score_category) {
                              //   chips.push(
                              //     <Chip
                              //       key="quality"
                              //       label={`Quality Score: ${capitalizeWords(item.quality_score_category)}`}
                              //       size="small"
                              //       variant="outlined"
                              //       sx={{
                              //         fontFamily: 'Roboto, sans-serif',
                              //         fontWeight: 500,
                              //         fontSize: '12px',
                              //         lineHeight: '14px',
                              //         letterSpacing: 0,
                              //         height: '22px',
                              //         borderRadius: '4px',
                              //         borderWidth: '1px',
                              //         borderColor: '#D4E4FC',
                              //         color: theme.palette.text.secondary,
                              //         px: '4px',
                              //         py: '0px',
                              //         textAlign: 'left',
                              //         alignItems: 'center',
                              //         display: 'flex',
                              //       }}
                              //     />
                              //   );
                              // }
                              if (item?.sector) {
                                let sectors = item.sector.split(';').map(s => s.trim()).filter(Boolean);
                                let formatted = sectors.map(capitalizeWords).join(', ');
                                chips.push(
                                  <Chip
                                    key="sector"
                                    label={`Sector: ${formatted}`}
                                    size="small"
                                    variant="outlined"
                                    sx={{
                                      fontFamily: 'Roboto, sans-serif',
                                      fontWeight: 500,
                                      fontSize: '12px',
                                      lineHeight: '14px',
                                      letterSpacing: 0,
                                      height: '22px',
                                      borderRadius: '4px',
                                      borderWidth: '1px',
                                      borderColor: '#D4E4FC',
                                      color: theme.palette.text.secondary,
                                      px: '4px',
                                      py: '0px',
                                      textAlign: 'left',
                                      alignItems: 'center',
                                      display: 'flex',
                                    }}
                                  />
                                );
                              }
                              return chips;
                            })()}
                          </Box>
                          {expandable && !alwaysExpanded && !isExpanded && (item?.intervention_name || item?.outcome_name) && (
                            <IconButton
                              size="small"
                              onClick={() => toggleSourceExpansion(item.id)}
                              sx={{
                                color: theme.palette.text.secondary,
                                position: 'absolute',
                                right: 12,
                                bottom: 12,
                                m: 0,
                                p: 0,
                              }}
                            >
                              <ExpandMoreIcon sx={{ width: '20px', height: '20px' }} />
                            </IconButton>
                          )}
                          {(expandable || alwaysExpanded) && (
                            <Collapse in={isExpanded || alwaysExpanded}>
                              <Box sx={{ mt: 1, pt: 1, borderTop: `1px solid ${theme.palette.divider}`, ml: '32px', mb: 0, pb: 0 }}>
                              {(interventionDetails || item?.intervention_name) && (
                                <Box sx={{ mb: (outcomeDetails || item?.outcome_name) ? 2 : 0 }}>
                                  <Typography
                                    variant="body2"
                                    sx={{
                                      fontFamily: 'Roboto, sans-serif',
                                      fontWeight: 500,
                                      color: theme.palette.text.primary,
                                      fontSize: '14px',
                                      lineHeight: '1.4',
                                      letterSpacing: '0.14px',
                                      mb: 0.5,
                                    }}
                                  >
                                    Intervention: {interventionDetails?.name || (item.intervention_name ? sentenceCase(item.intervention_name) : 'Unknown Intervention')}
                                  </Typography>
                                  <Typography
                                    variant="body2"
                                    sx={{
                                      fontFamily: 'Roboto, sans-serif',
                                      fontWeight: 400,
                                      color: theme.palette.text.primary,
                                      fontSize: '14px',
                                      lineHeight: '1.66',
                                      letterSpacing: '0.4px',
                                      ml: 0,
                                      mb: !alwaysExpanded ? 1 : 0,
                                    }}
                                  >
                                    Description: {interventionDetails?.description || sentenceCase(item?.intervention_details || 'No description available')}
                                  </Typography>
                                  {!alwaysExpanded && !(outcomeDetails || item?.outcome_name) && (
                                    <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 1 }}>
                                      <IconButton
                                        size="small"
                                        onClick={() => toggleSourceExpansion(item.id)}
                                        sx={{
                                          color: theme.palette.text.secondary,
                                          mb: 0,
                                        }}
                                      >
                                        <ExpandLessIcon sx={{ width: '20px', height: '20px' }} />
                                      </IconButton>
                                    </Box>
                                  )}
                                </Box>
                              )}

                              {(outcomeDetails || item?.outcome_name) && (
                                <Box sx={{ mb: 0 }}>
                                  <Typography
                                    variant="body2"
                                    sx={{
                                      fontFamily: 'Roboto, sans-serif',
                                      fontWeight: 500,
                                      color: theme.palette.text.primary,
                                      fontSize: '14px',
                                      lineHeight: '1.4',
                                      letterSpacing: '0.14px',
                                      mb: 0.5,
                                    }}
                                  >
                                    Outcome: {outcomeDetails?.name || (item.outcome_name ? sentenceCase(item.outcome_name) : 'Unknown Outcome')}
                                  </Typography>
                                  <Box sx={{ display: 'flex', flexDirection: 'row', alignItems: 'flex-end', width: '100%' }}>
                                    <Box sx={{ flex: '1 1 auto', minWidth: 0 }}>
                                      <Typography
                                        variant="body2"
                                        sx={{
                                          fontFamily: 'Roboto, sans-serif',
                                          fontWeight: 400,
                                          color: theme.palette.text.primary,
                                          fontSize: '14px',
                                          lineHeight: '1.66',
                                          letterSpacing: '0.4px',
                                          ml: 0,
                                        }}
                                      >
                                        Description: {outcomeDetails?.description || sentenceCase(item?.outcome_details || 'No description available')}
                                      </Typography>
                                    </Box>
                                    {!alwaysExpanded && (
                                      <Box sx={{ width: '40px', display: 'flex', justifyContent: 'flex-end', alignItems: 'flex-end', pl: 0 }}>
                                        <IconButton
                                          size="small"
                                          onClick={() => toggleSourceExpansion(item.id)}
                                          sx={{
                                            color: theme.palette.text.secondary,
                                            mb: 0,
                                            alignSelf: 'flex-end',
                                            p: 0,
                                          }}
                                        >
                                          <ExpandLessIcon sx={{ width: '20px', height: '20px' }} />
                                        </IconButton>
                                      </Box>
                                    )}
                                  </Box>
                                </Box>
                              )}
                            </Box>
                          </Collapse>
                          )}
                        </CardContent>
                      </Card>
                    </motion.div>
                  );
                })}
              </AnimatePresence>
            </Box>
          </Card>
        )
      ) : null}
    </>

  );
};

export default Sources;