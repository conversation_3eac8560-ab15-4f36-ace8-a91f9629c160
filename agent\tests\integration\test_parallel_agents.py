import asyncio
import pytest
import time
from src.agent.main import Agent


@pytest.mark.asyncio
async def test_sequential_vs_parallel():
    # Use the same config as in analytics.py
    agent_config = {
        "model_name": "gemini-2.0-flash-001",
        "temperature": 0.1,
        "max_tokens": 8192,
        "max_iterations": 10,
        "verbose": True,
        "bucket_name": "scihub-papers-processed",
        "use_gcp": True,
    }

    # Sequential execution
    start_time = time.time()
    agent1 = Agent(config=agent_config, conversation_id="sequential1")
    agent2 = Agent(config=agent_config, conversation_id="sequential2")

    try:
        result1 = await agent1.execute(
            "Are cash transfers programs efficient on stunting?"
        )
        result2 = await agent2.execute(
            "What strategies are most effective in minimizing postharvest loss?"
        )
        sequential_time = time.time() - start_time

        print("\nSequential Execution Results:")
        print(f"Q1 Response (first 200 chars): {result1[:200]}...")
        print(f"Q2 Response (first 200 chars): {result2[:200]}...")

        # Check that sequential agents maintained separate states
        assert len(agent1.history) > 0
        assert len(agent2.history) > 0
        assert agent1.history != agent2.history
        assert agent1.conversation_history != agent2.conversation_history
    finally:
        await asyncio.gather(agent1.cleanup(), agent2.cleanup())

    # Parallel execution
    start_time = time.time()
    agent3 = Agent(config=agent_config, conversation_id="parallel1")
    agent4 = Agent(config=agent_config, conversation_id="parallel2")

    try:
        results = await asyncio.gather(
            agent3.execute("Are cash transfers programs efficient on stunting?"),
            agent4.execute(
                "What strategies are most effective in minimizing postharvest loss?"
            ),
        )
        parallel_time = time.time() - start_time

        print("\nParallel Execution Results:")
        print(f"Q1 Response (first 200 chars): {results[0][:200]}...")
        print(f"Q2 Response (first 200 chars): {results[1][:200]}...")

        # Check that parallel agents maintained separate states
        assert len(agent3.history) > 0
        assert len(agent4.history) > 0
        assert agent3.history != agent4.history
        assert agent3.conversation_history != agent4.conversation_history

        # Verify we got valid responses
        assert len(results) == 2
        assert all(isinstance(result, str) for result in results)
    finally:
        await asyncio.gather(agent3.cleanup(), agent4.cleanup())

    print(f"\nSequential execution time: {sequential_time:.2f} seconds")
    print(f"Parallel execution time: {parallel_time:.2f} seconds")

    # The parallel execution should be notably faster than sequential
    assert (
        parallel_time < sequential_time
    ), f"Parallel ({parallel_time:.2f}s) should be faster than sequential ({sequential_time:.2f}s)"
