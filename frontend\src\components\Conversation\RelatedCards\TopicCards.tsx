import { useEffect, useState } from 'react';
import {
    Box,
    Typography,
    Card,
    CardContent,
    Grid,
    IconButton,
} from '@mui/material';
import NorthEastIcon from '@mui/icons-material/NorthEast';
import { useTheme } from '@mui/material/styles';
import {
    RELATED_TOPICS_TITLE,
    SUGGESTED_TOPICS_TITLE
} from "../../utils/labels";
import { get } from "../../services/apiService";
import { FollowUpResponse, Option } from "../../types/ConversationTypes";

interface TopicCardsProps {
    options: Option[];
    handleChange: (event: React.ChangeEvent<any>) => void;
    headline?: string;
    size: string;
    waitingForMessageId?: boolean;
    cardType: string;
    conversationId: string;
    streamingEnded: boolean;
    followupMessageId: string;
}
const TopicCards: React.FC<TopicCardsProps> = ({
    options,
    handleChange,
    headline = '',
    size,
    waitingForMessageId,
    cardType = 'card',
    conversationId,
    streamingStarted,
    streamingEnded,
    followupMessageId,
}) => {
    const theme = useTheme();
    const isCardTypePanel = (cardType === 'panel');
    const sizes = {
        small: {
            width: 142,
            fontSize: 14,
            variant: 'body2',
            p: { x: 8, y: 16 },
            height: 168,
        },
        medium: {
            width: 200,
            fontSize: 16,
            variant: 'body1',
            p: { x: 16, y: 16 },
            height: 200,
        },
    };

    const [optionsData, setOptionsData] = useState([]);
    const [optionsHeadline, setOptionsHeadline] = useState('');

    const filterValidOptions = (options) => {
        return options.filter(option =>
            option.value !== 'undefined' &&
            option.label &&
            option.description &&
            option.value
        );
    };

    useEffect(() => {
        if (options) {
            const validOptions = filterValidOptions(options);
            setOptionsData(validOptions);
        }
    }, [options]);

    useEffect(() => {
        if (headline) {
            setOptionsHeadline(headline);
        }
    }, [headline]);

    useEffect(() => {
        if (streamingEnded && followupMessageId) {
            fetchTopicCards();
        }
    }, [streamingEnded]);

    const fetchTopicCards = async () => {
        try {
            const response: FollowUpResponse = await get<FollowUpResponse>(`/conversations/${conversationId}/messages/${followupMessageId}`);
            if (response.success && response.data) {
                const options = response.data.choices.options;
                const headline = response.data.choices.type;
                const validOptions = filterValidOptions(options);
                setOptionsData(validOptions);
                setOptionsHeadline(headline ? headline : '');
            } else {
                console.error('Failed to fetch topic cards');
            }
        } catch (error) {
            console.error('Error fetching topic cards:', error);
        }
    };

    if (!optionsData || optionsData.length === 0) {
        return null;
    }

    const gridSizes = isCardTypePanel
        ? { xs: 12, sm: 12, md: 12, lg: 12 }
        : { xs: 6, sm: 3, md: 3, lg: 3 };

    return (
        <Box
            display="flex"
            flexDirection="column"
            alignItems="flex-start"
            gap={2}
            width="100%"
        >
            {optionsHeadline && (
                <Typography
                    variant="h5"
                    gutterBottom
                    sx={{ fontSize: '20px', fontWeight: 'bold' }}>
                    {optionsHeadline === 'related_topics' && RELATED_TOPICS_TITLE}
                    {optionsHeadline === 'suggested_topics' && SUGGESTED_TOPICS_TITLE}
                </Typography>
            )}
            <Grid
                id="cards-container"
                container
                spacing={isCardTypePanel ? 0 : 2}
                sx={{ flexDirection: !isCardTypePanel ? 'row' : 'column' }}
            >
                {optionsData.map((option, index) => (
                    <Grid
                        item
                        {...gridSizes}
                        key={`${option.ref}_${index}`}
                    >
                        <Box
                            sx={{
                                width: '100%',
                                marginBottom: '16px',
                                flex: 1,
                                p: 0,
                                m: 0,
                            }}>
                            <Card
                                onClick={() => (!waitingForMessageId || !streamingStarted) && handleChange(option, 'suggested_topics')}
                                sx={{
                                    width: {
                                        xs: '100%',
                                        sm: '100%',
                                        md: !(isCardTypePanel) ? sizes.medium.width : '100%',
                                    },
                                    height: {
                                        xs: 'auto',
                                        sm: 'auto',
                                        md: !isCardTypePanel ? `${sizes.medium.height}px` : undefined,
                                    },
                                    display: 'flex',
                                    flexDirection: 'column',
                                    boxShadow: 'none',
                                    cursor: (waitingForMessageId || streamingStarted) ? 'not-allowed' : 'pointer',
                                    borderRadius: isCardTypePanel ? undefined : '8px',
                                    overflow: 'hidden',
                                    pointerEvents: (waitingForMessageId || streamingStarted) ? 'none' : 'auto',
                                    transition: 'background-color 0.3s ease, color 0.2s ease',
                                    borderTop: index === 0 && isCardTypePanel ? `1px solid ${theme.palette.divider}` : undefined,
                                    borderBottom: isCardTypePanel && `1px solid ${theme.palette.divider}`,
                                    background: !isCardTypePanel ? theme.palette.background.paperElevationZero : theme.palette.background.default,
                                    color: theme.palette.text.primary,
                                    '&:hover': {
                                        backgroundColor: isCardTypePanel ? undefined : theme.palette.action.hover,
                                    },
                                }}>
                                <CardContent
                                    sx={{
                                        px: isCardTypePanel ? undefined : `${sizes[size].p.x}px`,
                                        py: '8px !important',
                                        height: {
                                            xs: 'auto',
                                            sm: 'auto',
                                            md: !isCardTypePanel ? `${sizes.medium.height}px` : 'auto',
                                        },
                                        boxSizing: 'border-box',
                                        display: 'flex',
                                        flexDirection: 'column',
                                        justifyContent: 'flex-start',
                                    }}
                                >
                                    <Box
                                        sx={{
                                            display: 'flex',
                                            alignItems: 'center',
                                            justifyContent: 'space-between',
                                            width: '100%',
                                            "&:hover, &.Mui-focusVisible": {
                                                color: isCardTypePanel && theme.palette.action.active,
                                                '& .MuiTypography-root': {
                                                    color: isCardTypePanel && theme.palette.action.active,
                                                },
                                                '& .MuiIconButton-root': {
                                                    color: isCardTypePanel && theme.palette.action.active,
                                                },
                                            },
                                        }}
                                    >
                                        <Typography
                                            variant={sizes[size].variant}
                                            sx={{
                                                fontSize: !isCardTypePanel ? `${sizes[size].fontSize}px` : `${sizes.medium.fontSize}px`,
                                                lineHeight: '150%',
                                                color: theme.palette.text.primary,
                                                letterSpacing: '0.15px',
                                                overflow: 'hidden',
                                                textOverflow: 'ellipsis',
                                                height: !isCardTypePanel ? `${sizes.medium.height}px` : undefined,
                                                wordBreak: 'break-word',
                                                transition: 'color 0.s ease',
                                                fontWeight: isCardTypePanel ? 500 : 400,
                                            }}
                                        >
                                            {option.description}
                                        </Typography>
                                        {isCardTypePanel && (
                                            <IconButton
                                                sx={{
                                                    ml: 1,
                                                    transition: 'color 0.s ease',
                                                    p: 1,
                                                    color: theme.palette.action.default,
                                                    '&:hover': {
                                                        color: theme.palette.action.active,
                                                    },
                                                }}
                                            >
                                                <NorthEastIcon fontSize="small" />
                                            </IconButton>
                                        )}
                                    </Box>
                                </CardContent>
                            </Card>
                        </Box>
                    </Grid>
                ))}
            </Grid>
        </Box>
    );
};

export default TopicCards;
