import asyncio
from datetime import datetime, timedelta
from src.agent.main import Agent
from src.services.cache import Cache

class AgentService:
    def __init__(self):
        self.configs = {
            "model_name": "gemini-2.0-flash-001",
            "temperature": 0.1,
            "max_tokens": 8192,
            "max_iterations": 10,
            "verbose": True,
            "bucket_name": "scihub-papers-processed",
            "use_gcp": True,
        }
        self.cache = Cache()

    async def generated_summary_data(self, conversation_id: str, message_id: str):
        cache_key = f"summary_data:{conversation_id}:{message_id}"

        async def get_summary_data():
            parsed_data = self.cache.get_json(cache_key)
            if not parsed_data:
                return None

            if parsed_data.get("stream_ended_at"):
                return parsed_data

            stream_started_at = parsed_data.get("stream_started_at")
            stream_age = datetime.now() - datetime.strptime(stream_started_at, "%Y-%m-%d %H:%M:%S.%f")
            if stream_age > timedelta(minutes=5):
                return parsed_data

            while True:
                await asyncio.sleep(5) # wait 5 seconds before trying again.
                return await get_summary_data()

        return await get_summary_data()

    async def generate_summary(self, conversation_id: str, message_id: str, query: str):
        cache_key = f"summary_data:{conversation_id}:{message_id}"
        stream_status = {
            "query": query,
            "stream_started_at": str(datetime.now()),
            "stream_ended_at": None,
            "summary_data": None,
            "summary_text": None,
        }
        self.cache.store_json(cache_key, stream_status)

        response = await self.execute(conversation_id, query)
        
        stream_status["summary_text"] = response.get("response", "")
        stream_status["summary_data"] = response.get("context", {}).get("tool_data", {})
        stream_status["stream_ended_at"] = str(datetime.now())
        
        self.cache.store_json(cache_key, stream_status)

        yield stream_status["summary_text"]

    async def execute(self, conversation_id: str, query: str):
        self.agent = Agent(
            self.configs,
            conversation_id=conversation_id,
        )
        response = await self.agent.execute(query=query)
        tool_data = self.agent.tool_manager.get_tool_data()

        structured_data = None
        if tool_data.get("structured_data"):
            structured_data = tool_data.get("structured_data").get("text", "")
        rag_results = None
        if tool_data.get("rag_results"):
            rag_results = tool_data.get("rag_results").get("documents", [])

        data_used_url = tool_data.get("data_used_url")
        url_post_processed = tool_data.get("url_post_processed")

        return {
            "response": response,
            "context": {
                "query": query,
                "conversation_id": self.agent.conversation_id,
                "tool_data": {
                    "structured_data": structured_data,
                    "rag_results": rag_results,
                    "data_url": data_used_url,
                    "url_post_processed": url_post_processed,
                },
            },
        }


agent_service = AgentService()
