import React, { useState, useEffect, useRef } from "react";
import * as d3 from "d3";
import { useTheme } from "@mui/material/styles";
import { Box } from "@mui/material";

interface EffectSizesPlot2Props {
  data: any;
  xDomain: any;
  onSelectStudy?: (id: string) => void;
  selectedStudy?: string;
}

const EffectSizesPlot2: React.FC<EffectSizesPlot2Props> = ({
  data,
  xDomain,
  onSelectStudy,
  selectedStudy,
}) => {
  const theme = useTheme();
  const containerRef = useRef<HTMLDivElement>(null);
  const [svgWidth, setSvgWidth] = useState(0);
  const [hoveredEffectSize, setHoveredEffectSize] = useState<number | null>(
    null
  );
  const [selectedEffectSize, setSelectedEffectSize] = useState<number | null>(
    null
  );

  useEffect(() => {
    if (containerRef.current) {
      const resizeObserver = new ResizeObserver((entries) => {
        for (const entry of entries) {
          setSvgWidth(entry.contentRect.width);
        }
      });
      resizeObserver.observe(containerRef.current);

      return () => resizeObserver.disconnect();
    }
  }, []);
  
  useEffect(() => {
    const studies = data?.[1];
    if (selectedStudy && studies && Array.isArray(studies)) {
      const selectedIndex = studies.findIndex((study: any) => {
        const studyId = String(study.id || study.paper_id);
        return studyId === selectedStudy;
      });
      
      if (selectedIndex >= 0) {
        setSelectedEffectSize(selectedIndex);
      }
    }
  }, [selectedStudy, data]);

  const margin = {
    top:20,
    right: 20,
    bottom: 0,
    left: 20,
  };

  const STUDY_HEIGHT = 16;
  const height = data[1].length * STUDY_HEIGHT + margin.top + margin.bottom;
  const width: number = containerRef.current?.offsetWidth || 0;
  const w: number = width - margin.left - margin.right;
  const h: number = height - margin.top - margin.bottom;

  const meanRange = [
    d3.mean(data[1], (d) => d.standardized_ci_lower),
    d3.mean(data[1], (d) => d.standardized_ci_upper),
  ];
  const meanEffectSize = meanRange[0] + (meanRange[1] - meanRange[0]) / 2;

  const xScale = d3
    .scaleLinear()
    // .domain([
    //   Math.min(
    //     0,
    //     d3.min(data[1], (d) => d.standardized_ci_lower)
    //   ),
    //   Math.max(
    //     0,
    //     d3.max(data[1], (d) => d.standardized_ci_upper)
    //   ),
    // ])
    .domain(xDomain)
    .range([0, w])
    .nice();

  const maxUpperLower = d3.max(data[1], (d) =>
    Math.max(Math.abs(d.hedges_d), Math.abs(d.hedges_d))
  );

  const cScale = d3
    .scaleQuantize()
    .domain([-maxUpperLower, maxUpperLower])
    .range([
      "#67001f",
      "#b2182b",
      "#d6604d",
      "#f4a582",
      "#fddbc7",
      "#f7f7f7",
      "#d1e5f0",
      "#92c5de",
      "#4393c3",
      "#2166ac",
      "#053061",
    ]);

  return (
    <Box
      className="funnel-plot-box"
      ref={containerRef}
      style={{
        background: "rgba(245, 249, 254, 1)",
        paddingTop: 10,
        paddingBottom: 4,
        marginBottom: 2,
      }}
    >
      <div>
        <svg width={svgWidth} height={height + margin.bottom + margin.top}>
          <defs>
            <linearGradient
              id={`gradient-${xScale.domain()[0]}-${xScale.domain()[1]}`}
              x1="0%"
              y1="0%"
              x2="100%"
              y2="0%"
            >
              {xScale.ticks(5).map((stop, idx) => {
                return (
                  <stop
                    key={stop}
                    offset={`${
                      ((stop - xScale.domain()[0]) /
                        (xScale.domain()[1] - xScale.domain()[0])) *
                      100
                    }%`}
                    stopColor={`${cScale(stop)}`}
                  />
                );
              })}
            </linearGradient>
          </defs>
          {w > 0 && (
            <g transform={`translate(${margin.left}, ${margin.top})`}>
              <line
                x1={xScale.range()[0]}
                y1={h}
                x2={xScale.range()[1]}
                y2={h}
                stroke="rgb(22 54 97)"
                opacity={0.3}
              />
              <line
                x1={xScale(0)}
                y1={h}
                x2={xScale(0)}
                y2={-margin.top}
                // stroke="#ccc"
                stroke="black"
                strokeDasharray="2 2"
              />

              {xScale
                .nice()
                .ticks()
                .map((tick, idx) => {
                  return (
                    <g key={tick + idx}>
                      <text
                        x={xScale(tick)}
                        y={h}
                        dy={12}
                        style={{
                          fontSize: 11,
                          textAnchor: "middle",
                          fill: "rgb(22 54 97)",
                        }}
                      >
                        {tick}
                      </text>
                    </g>
                  );
                })}

              <text
                x={w / 2}
                y={h}
                dy={30}
                style={{
                  fontSize: 12,
                  textAnchor: "middle",
                  fontWeight: "bold",
                  fill: "rgb(22 54 97)",
                }}
              >
                Effect Size
              </text>

              {data[1]
                .sort((a, b) => b.hedges_d - a.hedges_d)
                .map((study, i) => (
                  <g
                    key={`${study.outcome_id}-${study.intervention_id}-${study.paper_id}-${study.hedges_d}-${study.standardized_ci_lower}-${study.standardized_ci_upper}`}
                    transform={`translate(0, ${i * STUDY_HEIGHT})`}
                    className="study"
                    style={{ cursor: onSelectStudy ? "pointer" : undefined }}
                    onMouseOver={() => setHoveredEffectSize(i)}
                    onMouseOut={() => setHoveredEffectSize(null)}
                    onClick={() => {
                      setSelectedEffectSize(i);
                      if (onSelectStudy) {
                        // Debug log for study click
                        // eslint-disable-next-line no-console
                        console.log(
                          "EffectSizesPlot2: onSelectStudy",
                          study.id,
                          study.paper_id,
                          typeof study.id
                        );
                        onSelectStudy(String(study.id || study.paper_id));
                      }
                    }}
                  >
                    <rect
                      width={w}
                      height={STUDY_HEIGHT}
                      y={-STUDY_HEIGHT / 2}
                      fill={
                        selectedEffectSize === i || hoveredEffectSize === i
                          ? theme.palette.secondary.light
                          : "transparent"
                      }
                    />
                    <line
                      x1={xScale(study.standardized_ci_lower)}
                      x2={xScale(study.standardized_ci_upper)}
                      stroke="rgb(22 54 97)"
                    />
                    <line
                      x1={xScale(study.standardized_ci_lower)}
                      y1={-4}
                      x2={xScale(study.standardized_ci_lower)}
                      y2={4}
                      stroke="rgb(22 54 97)"
                    />
                    <line
                      x1={xScale(study.standardized_ci_upper)}
                      y1={-4}
                      x2={xScale(study.standardized_ci_upper)}
                      y2={4}
                      stroke="rgb(22 54 97)"
                    />
                    <g onClick={() => console.log(study)}>
                      <circle
                        className="back-outline"
                        cx={xScale(study.hedges_d)}
                        r={7}
                        fill={`${cScale(study.hedges_d)}`}
                        stroke="rgb(22 54 97)"
                        strokeWidth={
                          hoveredEffectSize === i || selectedEffectSize === i
                            ? 3
                            : 1
                        }
                      />
                    </g>
                  </g>
                ))}
            </g>
          )}
        </svg>
      </div>
    </Box>
  );
};

export default EffectSizesPlot2;
